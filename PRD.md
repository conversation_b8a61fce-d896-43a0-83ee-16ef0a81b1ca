# 徒步签到小程序产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品名称
徒步签到小程序

### 1.2 产品定位
一款专为徒步活动设计的签到管理小程序，帮助队长高效管理队员，确保活动安全有序进行。

### 1.3 目标用户
- **主要用户**：徒步活动队长/组织者
- **次要用户**：徒步活动参与者/队员
- **用户特征**：
  - 年龄：25-55岁
  - 职业：户外爱好者、企业团建组织者、旅行社导游等
  - 使用场景：户外徒步、登山、团建活动等

### 1.4 核心价值
- 提高活动组织效率
- 确保队员安全管理
- 简化签到流程
- 实时掌握队员状态

## 2. 功能需求

### 2.1 用户角色定义

#### 2.1.1 队长角色
- **权限**：创建活动、管理队员、发起喊话、查看统计、导出数据
- **职责**：活动组织、安全管理、人员统计

#### 2.1.2 副队长角色
- **权限**：协助管理队员、设置签到点、查看统计
- **职责**：辅助队长管理活动

#### 2.1.3 队员角色
- **权限**：签到、响应喊话、查看活动信息
- **职责**：按时签到、配合管理

### 2.2 核心功能模块

#### 2.2.1 角色选择模块
**功能描述**：用户首次进入应用时选择身份角色

**功能点**：
- 我是队长：进入队长控制台
- 我是队员：进入队员界面
- 加入活动：通过口令加入现有活动

**交互流程**：
1. 用户打开小程序
2. 显示角色选择页面
3. 用户点击对应角色
4. 跳转到相应功能页面

#### 2.2.2 队长控制台模块
**功能描述**：队长管理活动和队员的核心界面

**功能点**：
- **活动管理**
  - 创建新活动
  - 查看当前活动信息
  - 生成活动口令
  - 设置活动时间地点

- **实时统计**
  - 已签到人数统计
  - "我到了"响应人数
  - 未响应人数统计
  - 签到进度条显示

- **队员管理**
  - 查看队员状态列表
  - 队员签到状态分类显示
  - 队员联系方式管理
  - 权限设置（设置副队长）

- **喊话功能**
  - 发起全员喊话
  - 清点人数
  - 查看响应情况

- **数据导出**
  - 导出签到记录
  - 生成活动报告

**交互流程**：
1. 队长进入控制台
2. 查看活动概况和统计信息
3. 根据需要执行管理操作
4. 实时监控队员状态

#### 2.2.3 队员界面模块
**功能描述**：队员参与活动和签到的主要界面

**功能点**：
- **活动信息展示**
  - 当前活动名称
  - 队长信息
  - 活动时间安排
  - 集合地点信息

- **签到功能**
  - 一键签到
  - 位置验证
  - 签到状态显示

- **喊话响应**
  - 接收队长喊话通知
  - 快速响应"我到了"
  - 查看喊话历史

- **个人状态**
  - 查看自己的签到状态
  - 签到时间记录
  - 活动参与历史

- **团队信息**
  - 团队总人数
  - 已签到人数
  - "我到了"人数

**交互流程**：
1. 队员进入界面
2. 查看活动信息和个人状态
3. 到达指定地点后签到
4. 响应队长喊话

#### 2.2.4 加入活动模块
**功能描述**：通过活动口令快速加入活动

**功能点**：
- 口令输入界面
- 口令验证
- 活动信息确认
- 自动加入队员列表

**交互流程**：
1. 用户选择"加入活动"
2. 输入6位数字口令
3. 系统验证口令有效性
4. 显示活动信息供确认
5. 确认后加入活动

#### 2.2.5 创建活动模块
**功能描述**：队长创建新的徒步活动

**功能点**：
- **基本信息设置**
  - 活动名称
  - 活动描述
  - 活动类型

- **队员信息管理**
  - Excel批量导入队员
  - 手动添加队员
  - 队员信息编辑
  - 权限分配

- **地点信息设置**
  - 集合地点
  - 目的地
  - GPS定位
  - 地址描述

- **时间安排**
  - 集合时间
  - 出发时间
  - 预计返程时间

- **活动口令生成**
  - 自动生成6位数字口令
  - 口令分享功能

**交互流程**：
1. 队长点击"创建活动"
2. 填写活动基本信息
3. 导入或添加队员信息
4. 设置时间地点
5. 确认创建并获得活动口令

#### 2.2.6 成员管理模块
**功能描述**：队长管理队员信息和权限

**功能点**：
- **成员列表**
  - 按角色分类显示（队长/副队长/队员）
  - 成员基本信息展示
  - 签到状态显示
  - 联系方式快速拨打

- **批量操作**
  - Excel导入成员
  - 批量权限设置
  - 批量消息发送

- **权限管理**
  - 设置副队长
  - 权限分配
  - 角色变更

- **成员搜索**
  - 按姓名搜索
  - 按手机号搜索
  - 按状态筛选

**交互流程**：
1. 队长进入成员管理
2. 查看成员列表和状态
3. 执行管理操作
4. 设置权限和角色

## 3. 技术需求

### 3.1 技术架构
- **前端**：微信小程序原生开发
- **后端**：微信云开发
- **数据库**：云数据库
- **存储**：云存储
- **云函数**：业务逻辑处理

### 3.2 数据库设计

#### 3.2.1 用户表 (users)
```javascript
{
  _id: String,           // 用户ID
  openid: String,        // 微信openid
  nickname: String,      // 用户昵称
  avatar: String,        // 头像URL
  phone: String,         // 手机号
  realName: String,      // 真实姓名
  role: String,          // 角色：main_captain/vice_captain/member
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

#### 3.2.2 队伍表 (teams)
```javascript
{
  _id: String,           // 队伍ID
  name: String,          // 队伍名称
  description: String,   // 队伍描述
  mainCaptain: String,   // 主队长用户ID
  viceCaptains: Array,   // 副队长用户ID数组
  inviteCode: String,    // 邀请码
  maxMembers: Number,    // 最大成员数
  memberCount: Number,   // 当前成员数
  status: String,        // 状态：active/inactive
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

#### 3.2.3 活动表 (activities)
```javascript
{
  _id: String,           // 活动ID
  teamId: String,        // 队伍ID
  name: String,          // 活动名称
  description: String,   // 活动描述
  schedule: {            // 时间安排
    meetingDate: String, // 集合日期
    meetingTime: String, // 集合时间
    returnTime: String   // 返程时间
  },
  meetingLocation: {     // 集合地点
    latitude: Number,    // 纬度
    longitude: Number,   // 经度
    address: String      // 地址描述
  },
  destination: {         // 目的地
    latitude: Number,
    longitude: Number,
    address: String
  },
  status: String,        // 状态：planning/active/completed/cancelled
  createTime: Date,
  updateTime: Date
}
```

#### 3.2.4 签到点表 (checkin_points)
```javascript
{
  _id: String,           // 签到点ID
  activityId: String,    // 活动ID
  name: String,          // 签到点名称
  location: {            // 位置信息
    latitude: Number,
    longitude: Number,
    address: String
  },
  sequence: Number,      // 签到顺序
  radius: Number,        // 签到半径（米）
  createTime: Date
}
```

#### 3.2.5 签到记录表 (checkin_records)
```javascript
{
  _id: String,           // 记录ID
  activityId: String,    // 活动ID
  pointId: String,       // 签到点ID
  userId: String,        // 用户ID
  checkinTime: Date,     // 签到时间
  location: {            // 签到位置
    latitude: Number,
    longitude: Number
  },
  type: String,          // 签到类型：normal/shout
  status: String,        // 状态：success/failed
  createTime: Date
}
```

#### 3.2.6 喊话记录表 (shout_records)
```javascript
{
  _id: String,           // 喊话ID
  activityId: String,    // 活动ID
  captainId: String,     // 发起人ID
  content: String,       // 喊话内容
  responses: Array,      // 响应用户ID数组
  createTime: Date
}
```

### 3.3 API接口设计

#### 3.3.1 用户相关接口
- `POST /api/user/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息

#### 3.3.2 活动相关接口
- `POST /api/activity/create` - 创建活动
- `GET /api/activity/list` - 获取活动列表
- `GET /api/activity/detail` - 获取活动详情
- `PUT /api/activity/update` - 更新活动信息
- `POST /api/activity/join` - 加入活动

#### 3.3.3 签到相关接口
- `POST /api/checkin/create` - 创建签到
- `GET /api/checkin/list` - 获取签到记录
- `GET /api/checkin/stats` - 获取签到统计

#### 3.3.4 喊话相关接口
- `POST /api/shout/create` - 发起喊话
- `POST /api/shout/respond` - 响应喊话
- `GET /api/shout/list` - 获取喊话记录

## 4. 用户体验设计

### 4.1 界面设计原则
- **简洁明了**：界面简洁，操作直观
- **信息层次**：重要信息突出显示
- **一致性**：保持设计风格统一
- **易用性**：符合用户使用习惯

### 4.2 交互设计
- **快速操作**：常用功能一键完成
- **状态反馈**：及时提供操作反馈
- **错误处理**：友好的错误提示
- **加载状态**：合理的加载动画

### 4.3 视觉设计
- **色彩搭配**：使用户外主题色彩
- **图标设计**：简洁易懂的图标
- **字体层次**：清晰的信息层级
- **空间布局**：合理的留白和间距

## 5. 业务流程

### 5.1 活动创建流程
1. 队长选择"我是队长"
2. 进入队长控制台
3. 点击"创建活动"
4. 填写活动基本信息
5. 导入队员信息（Excel或手动）
6. 设置时间地点
7. 生成活动口令
8. 分享口令给队员

### 5.2 队员加入流程
1. 队员选择"加入活动"
2. 输入活动口令
3. 验证口令有效性
4. 显示活动信息
5. 确认加入活动
6. 进入队员界面

### 5.3 签到流程
1. 队员到达集合地点
2. 打开小程序
3. 点击"我已上车"按钮
4. 系统验证位置信息
5. 签到成功，更新状态
6. 队长实时查看签到情况

### 5.4 喊话流程
1. 队长发起喊话
2. 系统推送通知给所有队员
3. 队员收到通知
4. 队员点击"我到了"响应
5. 队长查看响应情况
6. 统计未响应人员

## 6. 非功能性需求

### 6.1 性能要求
- **响应时间**：页面加载时间 < 2秒
- **并发用户**：支持100+用户同时在线
- **数据同步**：实时数据更新延迟 < 3秒

### 6.2 安全要求
- **数据加密**：敏感数据传输加密
- **权限控制**：严格的角色权限管理
- **隐私保护**：用户信息保护

### 6.3 兼容性要求
- **微信版本**：支持微信7.0+
- **系统兼容**：iOS 10+, Android 6.0+
- **设备适配**：支持主流手机屏幕尺寸

### 6.4 可用性要求
- **系统稳定性**：99.9%可用性
- **错误恢复**：自动错误恢复机制
- **离线支持**：基本功能离线可用

## 7. 运营需求

### 7.1 数据统计
- 用户活跃度统计
- 活动创建和参与统计
- 签到成功率统计
- 功能使用情况分析

### 7.2 用户反馈
- 意见反馈收集
- 问题报告处理
- 用户满意度调研

### 7.3 版本迭代
- 功能优化和新增
- 性能提升
- 用户体验改进

## 8. 风险评估

### 8.1 技术风险
- **网络依赖**：需要稳定的网络连接
- **定位精度**：GPS定位可能存在误差
- **设备兼容**：不同设备的兼容性问题

### 8.2 业务风险
- **用户接受度**：新用户学习成本
- **竞品压力**：市场竞争激烈
- **使用场景**：户外环境网络不稳定

### 8.3 解决方案
- **技术方案**：多重定位方式、离线缓存
- **产品方案**：简化操作流程、增强引导
- **运营方案**：用户教育、客服支持

## 9. 项目计划

### 9.1 开发阶段
- **第一阶段**（2周）：核心功能开发
  - 角色选择
  - 基础签到功能
  - 活动创建

- **第二阶段**（2周）：高级功能开发
  - 喊话功能
  - 成员管理
  - 数据统计

- **第三阶段**（1周）：优化和测试
  - 性能优化
  - 功能测试
  - 用户体验优化

### 9.2 测试阶段
- **内部测试**：功能测试、性能测试
- **用户测试**：真实场景测试
- **优化迭代**：根据测试结果优化

### 9.3 发布阶段
- **小程序审核**：提交微信审核
- **正式发布**：上线运营
- **运营推广**：用户推广和运营

## 10. 成功指标

### 10.1 用户指标
- 月活跃用户数 > 1000
- 用户留存率 > 60%
- 用户满意度 > 4.5/5

### 10.2 业务指标
- 活动创建成功率 > 95%
- 签到成功率 > 90%
- 功能使用率 > 80%

### 10.3 技术指标
- 系统可用性 > 99.9%
- 页面加载时间 < 2秒
- 错误率 < 1%

---

**文档版本**：v1.0  
**创建日期**：2024年1月  
**最后更新**：2024年1月  
**负责人**：产品团队