**Assistant:**

基于您的需求，我来设计徒步活动签到微信小程序的整体方案。

## 📋 需求分析和功能规划

### 核心需求总结
- **队伍规模**：10-50人，支持多个队长管理
- **签到场景**：集合出发签到 → 上车签到 → 服务区签到 → 活动结束上车签到 → 活动结束 
- **喊道功能**：队员在app上操作表示"已到达"
- **通讯功能**：队长和队员之间双向一键拨打电话
- **信息录入**：队员手机号姓名快捷录入

### 主要功能模块
1. **队伍管理模块**
2. **签到管理模块** 
3. **通讯联系模块**
4. **成员管理模块**
5. **活动流程管理模块**

## 👥 用户角色和权限设计

### 角色定义
- **主队长**：创建队伍、管理所有队员、设置签到点、查看全部数据,可以导入excel（队员数据 队员名称，队员手机号），也可以发起，让大家通过小程序签到
- **副队长**：协助管理队员、查看签到状态、联系队员
- **队员**：参与签到、喊道报到、查看活动信息

### 权限矩阵
| 功能 | 主队长 | 副队长 | 队员 |
|------|--------|--------|------|
| 创建队伍 | ✅ | ❌ | ❌ |
| 添加队员 | ✅ | ✅ | ❌ |
| 设置签到点 | ✅ | ✅ | ❌ |
| 签到操作 | ✅ | ✅ | ✅ |
| 喊道报到 | ✅ | ✅ | ✅ |
| 查看签到统计 | ✅ | ✅ | ❌ |
| 一键拨打电话 | ✅ | ✅ | ✅ |

## 🗄️ 数据库结构设计

### 核心数据表
```sql
-- 队伍表
teams (
  id, name, description, main_captain_id, 
  create_time, status, max_members
)

-- 用户表  
users (
  id, openid, nickname, phone, real_name,
  avatar, create_time
)

-- 队伍成员表
team_members (
  id, team_id, user_id, role, 
  join_time, status
)

-- 签到点表
checkin_points (
  id, team_id, name, location, 
  sequence, create_time, status
)

-- 签到记录表
checkin_records (
  id, team_id, user_id, point_id,
  checkin_time, location, status
)
```

## 🏗️ 微信小程序架构设计

```mermaid
graph TB
    A[微信小程序前端] --> B[API网关]
    B --> C[用户服务]
    B --> D[队伍服务] 
    B --> E[签到服务]
    B --> F[通讯服务]
    
    C --> G[用户数据库]
    D --> H[队伍数据库]
    E --> I[签到数据库]
    F --> J[消息队列]
    
    K[微信API] --> A
    L[地图服务] --> A
    M[推送服务] --> A
```

## 🔧 核心功能模块设计

### 1. 队长快速建立队伍
- **创建队伍**：输入队伍名称、活动描述、预计人数
- **生成邀请码**：6位数字邀请码，有效期24小时
- **分享功能**：生成小程序码，支持微信群分享
- **队长设置**：支持设置多个副队长

### 2. 队伍成员上车签到
- **多点签到**：支持设置多个签到点（集合点、上车点、服务区、终点）
- **地理位置验证**：基于GPS定位，设置签到范围（如500米内）
- **签到状态**：实时显示已签到/未签到人数
- **异常处理**：支持队长代签到功能

### 3. 队员喊道功能
- **一键报到**：大按钮设计，点击即表示"已到达"
- **状态广播**：自动在队伍群里显示"XXX已到达"
- **位置共享**：可选择是否同时分享当前位置
- **时间记录**：记录每次喊道的时间戳

### 4. 一键打电话功能
- **联系人列表**：显示队长、副队长、队员电话
- **快速拨号**：点击头像直接拨打电话
- **紧急联系**：置顶显示队长联系方式
- **通话记录**：记录通话时间和对象

### 5. 队员信息快捷录入
- **微信授权**：自动获取昵称、头像
- **手机号验证**：短信验证码验证
- **真实姓名**：手动输入或身份证OCR识别
- **Excel批量导入**：支持上传Excel文件，包含两列：人员姓名和手机号
  - 支持.xlsx和.xls格式
  - 自动验证手机号格式
  - 导入前预览数据
  - 支持批量添加到队伍
  - 队长创建队伍时可选择Excel导入或手动添加成员

## 📱 用户界面设计规划

### 主要页面结构
```
├── 首页
│   ├── 我的队伍列表
│   ├── 加入队伍（扫码/邀请码）
│   └── 创建队伍
├── 队伍详情页
│   ├── 队伍信息
│   ├── 成员列表
│   ├── 签到状态
│   └── 联系功能
├── 签到页面
│   ├── 当前签到点
│   ├── 签到按钮
│   ├── 喊道按钮
│   └── 签到统计
└── 个人中心
    ├── 个人信息
    ├── 我的队伍
    └── 设置
```

### 关键交互设计
- **大按钮设计**：签到和喊道按钮突出显示
- **状态指示**：用颜色区分已签到/未签到状态
- **实时更新**：签到状态实时刷新
- **操作反馈**：每个操作都有明确的成功/失败提示

## 🎨 UI设计要求

### 设计原则
- **极简精美**：界面简洁而精致，专注核心功能，避免过度设计
- **双视角体验**：
  - 队长视角：管理工具化，操作高效，信息全面
  - 队员视角：操作简化，交互丝滑，体验友好
- **易用性**：一键操作，减少用户学习成本
- **现代感**：大卡片设计，圆角元素，渐变色彩

### 配色方案（参考灰白现代风格）
- **主色调**：深色主题 (#1C1C1E) 配合白色卡片
- **强调色**：蓝色 (#007AFF) 用于重要操作按钮
- **背景色**：浅灰背景 (#F2F2F7) 和纯白卡片 (#FFFFFF)
- **文字色**：
  - 主文字：深黑色 (#000000)
  - 辅助文字：中灰色 (#8E8E93)
  - 弱化文字：浅灰色 (#C7C7CC)
- **状态色**：
  - 成功：绿色 (#34C759)
  - 警告：橙色 (#FF9500)
  - 错误：红色 (#FF3B30)

### 关键设计元素
1. **大卡片布局**：使用大尺寸圆角卡片承载主要功能
2. **入场券设计**：队伍信息采用票据样式设计，增加仪式感
3. **圆形头像**：用户头像统一使用圆形设计
4. **渐变按钮**：重要操作使用渐变色按钮，增加视觉吸引力
5. **图标系统**：使用线性图标风格，保持视觉一致性

### 组件设计
- **卡片式布局**：使用圆角卡片承载内容，增加层次感
- **大按钮**：签到、喊道等主要操作使用大尺寸按钮
- **头像展示**：圆形头像配合姓名显示
- **状态指示器**：使用颜色和图标清晰表示状态
- **滑动交互**：队员签到使用丝滑的滑动手势

### 页面布局
- **顶部导航**：简洁的标题栏，必要时显示返回按钮
- **内容区域**：主要内容使用卡片布局，间距适中
- **底部操作**：重要操作按钮固定在底部
- **浮动按钮**：快速操作使用浮动按钮

### 动效设计
- **页面切换**：使用微信小程序原生切换动画
- **按钮反馈**：点击时有轻微的缩放和颜色变化
- **状态变化**：签到状态变化时有平滑的过渡动画
- **加载状态**：使用优雅的加载动画

## 🛠️ 技术选型

### 前端技术栈
- **框架**：微信小程序原生开发
- **UI组件**：WeUI / Vant Weapp
- **状态管理**：小程序全局数据管理
- **地图服务**：腾讯地图API

### 后端技术栈
- **语言**：Node.js / Python
- **框架**：Express / FastAPI
- **数据库**：MySQL + Redis
- **消息推送**：微信模板消息
- **部署**：Docker + 云服务器

## 📊 系统流程图

```mermaid
sequenceDiagram
    participant C as 队长
    participant S as 系统
    participant M as 队员
    
    C->>S: 创建队伍
    S->>C: 返回邀请码
    C->>M: 分享邀请码
    M->>S: 加入队伍
    S->>C: 通知新成员加入
    
    C->>S: 设置签到点
    S->>M: 推送签到通知
    M->>S: 执行签到
    S->>C: 更新签到状态
    
    M->>S: 喊道报到
    S->>C: 广播队员状态
```

这个设计方案涵盖了您提到的所有核心需求，包括队长快速建队、多场景签到、喊道功能、一键通话和信息录入。整个系统采用模块化设计，便于后续扩展和维护。

您对这个整体设计方案有什么意见或需要调整的地方吗？

<update_todo_list>
<todos>
[x] 需求分析和功能规划
[x] 用户角色和权限设计
[x] 数据库结构设计
[x] 微信小程序架构设计
[x] 核心功能模块设计
[x] 用户界面设计规划
[-] 技术选型和开发环境搭建
[ ] 后端API接口设计
[ ] 微信小程序前端开发
[ ] 测试方案制定
[ ] 部署和上线方案
</todos>
</update_todo_list>

---

**User:**

[update_todo_list] Result:
Todo list updated successfully.
<environment_details>
# VSCode Visible Files
src/pages/masterSavleConfig/constants.js

# VSCode Open Tabs
src/pages/masterSavleConfig/constants.js,src/pages/masterSavleConfig/api.js

# Current Time
Current time in ISO 8601 UTC format: 2025-08-08T08:23:09.465Z
User time zone: Asia/Shanghai, UTC+8:00

# Current Cost
$0.23

# Current Mode
<slug>architect</slug>
<name>Architect</name>
<model>anthropic/claude-sonnet-4</model>
====

REMINDERS

Below is your current list of reminders for this task. Keep them updated as you progress.

| # | Content | Status |
|---|---------|--------|
| 1 | 需求分析和功能规划 | Completed |
| 2 | 用户角色和权限设计 | Completed |
| 3 | 数据库结构设计 | Completed |
| 4 | 微信小程序架构设计 | Completed |
| 5 | 核心功能模块设计 | Completed |
| 6 | 用户界面设计规划 | Completed |
| 7 | 技术选型和开发环境搭建 | In Progress |
| 8 | 后端API接口设计 | Pending |
| 9 | 微信小程序前端开发 | Pending |
| 10 | 测试方案制定 | Pending |
| 11 | 部署和上线方案 | Pending |


IMPORTANT: When task status changes, remember to call the `update_todo_list` tool to update your progress.

</environment_details>

---

**Assistant:**

<ask_followup_question>
<question>您对这个整体设计方案是否满意？接下来您希望我重点完善哪个方面？是继续详细设计API接口，还是开始制定具体的开发实施计划？</question>
<follow_up>
<suggest mode="code">开始实际开发微信小程序代码</suggest>
<suggest>继续完善后端API接口设计和数据库详细设计</suggest>
<suggest>制定详细的开发时间计划和里程碑</suggest>
<suggest>设计方案很好，我想了解具体的开发成本和周期评估</suggest>
</follow_up>
</ask_followup_question>



# 徒步活动签到微信小程序 - 完整执行计划

## 📋 项目概述

基于需求文档，开发一个徒步活动签到微信小程序，支持队长快速建队、成员签到、喊道功能、一键通话等核心功能。采用微信小程序云开发技术栈，无需自建服务器。

### 核心需求
- 队伍规模：10-50人，支持多个队长管理
- 签到场景：集合出发签到 → 上车签到 → 服务区签到 → 活动结束上车签到
- 喊道功能：队员在app上操作表示"已到达"
- 通讯功能：队长和队员之间双向一键拨打电话
- 信息录入：队员手机号姓名快捷录入

## 🚀 第一阶段：项目初始化和环境搭建

### 1.1 开发环境准备
**时间估计：1天**

#### 操作步骤：
1. **下载安装微信开发者工具**
   ```bash
   # 下载地址
   https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   ```

2. **申请小程序账号**
   - 访问：https://mp.weixin.qq.com/
   - 选择"小程序" -> "立即注册"
   - 完成企业认证（个人开发可选个人类型）
   - 获取 AppID

3. **开通微信支付（可选）**
   - 如需支付功能，在小程序后台开通微信支付
   - 配置商户号和支付密钥

### 1.2 创建项目和云开发环境
**时间估计：0.5天**

#### 操作步骤：
1. **创建小程序项目**
   ```bash
   # 在微信开发者工具中
   1. 点击"+"创建项目
   2. 填写项目名称：徒步签到小程序
   3. 填写AppID
   4. 勾选"云开发 QuickStart 项目"
   5. 选择JavaScript语言
   ```

2. **初始化云开发环境**
   ```bash
   # 在开发者工具中
   1. 点击"云开发"按钮
   2. 按照提示开通云开发
   3. 创建环境（建议命名：hiking-prod）
   4. 等待10分钟环境初始化完成
   ```

3. **配置项目结构**
   ```
   miniprogram/
   ├── app.js                  # 小程序主入口
   ├── app.json               # 全局配置
   ├── app.wxss               # 全局样式
   ├── sitemap.json           # 爬虫配置
   ├── pages/                 # 页面目录
   │   ├── index/             # 首页
   │   ├── team/              # 队伍管理
   │   ├── checkin/           # 签到页面
   │   ├── profile/           # 个人中心
   │   └── components/        # 公共组件
   └── cloudfunctions/        # 云函数目录
       ├── login/             # 用户登录
       ├── team/              # 队伍管理
       ├── checkin/           # 签到功能
       └── notification/      # 消息推送
   ```

## 🗄️ 第二阶段：数据库设计和云函数开发

### 2.1 云数据库设计
**时间估计：1天**

#### 操作步骤：
1. **在云开发控制台创建数据集合**
   ```javascript
   // 用户表 (users)
   {
     _id: "用户ID",
     _openid: "微信openid",
     nickname: "昵称",
     avatar: "头像URL",
     phone: "手机号",
     realName: "真实姓名",
     createTime: "创建时间"
   }

   // 队伍表 (teams)
   {
     _id: "队伍ID",
     name: "队伍名称",
     description: "活动描述",
     mainCaptain: "主队长openid",
     viceCaptains: ["副队长openid数组"],
     inviteCode: "邀请码",
     maxMembers: 50,
     status: "active/ended",
     createTime: "创建时间"
   }

   // 队伍成员表 (team_members)
   {
     _id: "记录ID",
     teamId: "队伍ID",
     openid: "用户openid",
     role: "main_captain/vice_captain/member",
     joinTime: "加入时间",
     status: "active/left"
   }

   // 签到点表 (checkin_points)
   {
     _id: "签到点ID",
     teamId: "队伍ID",
     name: "签到点名称",
     location: {
       latitude: "纬度",
       longitude: "经度",
       address: "地址描述"
     },
     sequence: 1,
     radius: 500,
     createTime: "创建时间"
   }

   // 签到记录表 (checkin_records)
   {
     _id: "记录ID",
     teamId: "队伍ID",
     pointId: "签到点ID",
     openid: "用户openid",
     checkinTime: "签到时间",
     location: {
       latitude: "签到纬度",
       longitude: "签到经度"
     },
     type: "normal/shout", // 正常签到或喊道
     status: "success"
   }
   ```

2. **设置数据库权限**
   ```javascript
   // 在云开发控制台设置每个集合的权限
   // 建议设置为"仅创建者可读写"，通过云函数控制访问权限
   ```

### 2.2 核心云函数开发
**时间估计：3天**

#### 2.2.1 用户登录云函数 (login)
```javascript
// cloudfunctions/login/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  const { userInfo } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    // 查询用户是否存在
    const userRes = await db.collection('users').where({
      _openid: OPENID
    }).get()
    
    if (userRes.data.length === 0) {
      // 新用户，创建记录
      await db.collection('users').add({
        data: {
          _openid: OPENID,
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl,
          createTime: new Date()
        }
      })
    } else {
      // 更新用户信息
      await db.collection('users').where({
        _openid: OPENID
      }).update({
        data: {
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl
        }
      })
    }
    
    return {
      success: true,
      openid: OPENID
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}
```

#### 2.2.2 队伍管理云函数 (team)
```javascript
// cloudfunctions/team/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()
  
  switch (action) {
    case 'create':
      return await createTeam(data, OPENID)
    case 'join':
      return await joinTeam(data, OPENID)
    case 'getMyTeams':
      return await getMyTeams(OPENID)
    case 'getTeamDetail':
      return await getTeamDetail(data.teamId, OPENID)
    case 'addMember':
      return await addMember(data, OPENID)
    default:
      return { success: false, error: '未知操作' }
  }
}

// 创建队伍
async function createTeam(data, openid) {
  const inviteCode = generateInviteCode()
  
  try {
    // 创建队伍
    const teamRes = await db.collection('teams').add({
      data: {
        name: data.name,
        description: data.description,
        mainCaptain: openid,
        viceCaptains: [],
        inviteCode: inviteCode,
        maxMembers: data.maxMembers || 50,
        status: 'active',
        createTime: new Date()
      }
    })
    
    // 添加队长为成员
    await db.collection('team_members').add({
      data: {
        teamId: teamRes._id,
        openid: openid,
        role: 'main_captain',
        joinTime: new Date(),
        status: 'active'
      }
    })
    
    return {
      success: true,
      teamId: teamRes._id,
      inviteCode: inviteCode
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

// 生成6位邀请码
function generateInviteCode() {
  return Math.random().toString().slice(-6)
}
```

#### 2.2.3 签到功能云函数 (checkin)
```javascript
// cloudfunctions/checkin/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()
  
  switch (action) {
    case 'createPoint':
      return await createCheckinPoint(data, OPENID)
    case 'checkin':
      return await performCheckin(data, OPENID)
    case 'shout':
      return await performShout(data, OPENID)
    case 'getPoints':
      return await getCheckinPoints(data.teamId)
    case 'getRecords':
      return await getCheckinRecords(data.teamId)
    default:
      return { success: false, error: '未知操作' }
  }
}

// 创建签到点
async function createCheckinPoint(data, openid) {
  try {
    // 验证用户权限（队长或副队长）
    const memberRes = await db.collection('team_members').where({
      teamId: data.teamId,
      openid: openid,
      role: db.command.in(['main_captain', 'vice_captain'])
    }).get()
    
    if (memberRes.data.length === 0) {
      return { success: false, error: '无权限操作' }
    }
    
    const result = await db.collection('checkin_points').add({
      data: {
        teamId: data.teamId,
        name: data.name,
        location: data.location,
        sequence: data.sequence,
        radius: data.radius || 500,
        createTime: new Date()
      }
    })
    
    return { success: true, pointId: result._id }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 执行签到
async function performCheckin(data, openid) {
  try {
    // 检查距离是否在范围内
    const distance = calculateDistance(
      data.userLocation.latitude,
      data.userLocation.longitude,
      data.pointLocation.latitude,
      data.pointLocation.longitude
    )
    
    if (distance > data.radius) {
      return { success: false, error: '不在签到范围内' }
    }
    
    // 检查是否已经签到
    const existingRecord = await db.collection('checkin_records').where({
      teamId: data.teamId,
      pointId: data.pointId,
      openid: openid
    }).get()
    
    if (existingRecord.data.length > 0) {
      return { success: false, error: '已经签到过了' }
    }
    
    // 创建签到记录
    await db.collection('checkin_records').add({
      data: {
        teamId: data.teamId,
        pointId: data.pointId,
        openid: openid,
        checkinTime: new Date(),
        location: data.userLocation,
        type: 'normal',
        status: 'success'
      }
    })
    
    return { success: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 计算两点距离（米）
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371e3 // 地球半径（米）
  const φ1 = lat1 * Math.PI/180
  const φ2 = lat2 * Math.PI/180
  const Δφ = (lat2-lat1) * Math.PI/180
  const Δλ = (lon2-lon1) * Math.PI/180
  
  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  
  return R * c
}
```

## 📱 第三阶段：前端页面开发

### 3.1 全局配置
**时间估计：0.5天**

#### app.json 配置
```json
{
  "pages": [
    "pages/index/index",
    "pages/team/create/create",
    "pages/team/detail/detail",
    "pages/team/join/join",
    "pages/checkin/checkin",
    "pages/profile/profile"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#1aad19",
    "navigationBarTitleText": "徒步签到",
    "navigationBarTextStyle": "white"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#1aad19",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "images/icons/home.png",
        "selectedIconPath": "images/icons/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/profile/profile",
        "iconPath": "images/icons/usercenter.png",
        "selectedIconPath": "images/icons/usercenter-active.png",
        "text": "我的"
      }
    ]
  },
  "cloud": true,
  "style": "v2",
  "sitemapLocation": "sitemap.json"
}
```

#### app.js 配置
```javascript
App({
  onLaunch: function () {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'your-env-id', // 替换为你的环境ID
        traceUser: true,
      })
    }
    
    // 登录
    this.globalLogin()
  },
  
  globalLogin() {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        wx.cloud.callFunction({
          name: 'login',
          data: {
            userInfo: res.userInfo
          },
          success: (res) => {
            if (res.result.success) {
              this.globalData.userInfo = res.userInfo
              this.globalData.openid = res.result.openid
            }
          }
        })
      }
    })
  },
  
  globalData: {
    userInfo: null,
    openid: null
  }
})
```

### 3.2 首页开发
**时间估计：1天**

#### pages/index/index.wxml
```html
<view class="container">
  <!-- 用户信息 -->
  <view class="user-info" wx:if="{{userInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>
  
  <!-- 功能按钮 -->
  <view class="action-buttons">
    <button class="action-btn create-btn" bindtap="createTeam">
      <image class="btn-icon" src="/images/icons/create.png"></image>
      <text>创建队伍</text>
    </button>
    
    <button class="action-btn join-btn" bindtap="joinTeam">
      <image class="btn-icon" src="/images/icons/join.png"></image>
      <text>加入队伍</text>
    </button>
  </view>
  
  <!-- 我的队伍列表 -->
  <view class="my-teams">
    <view class="section-title">我的队伍</view>
    <scroll-view class="teams-list" scroll-y="true">
      <view class="team-item" wx:for="{{teams}}" wx:key="id" bindtap="enterTeam" data-team="{{item}}">
        <view class="team-info">
          <view class="team-name">{{item.name}}</view>
          <view class="team-desc">{{item.description}}</view>
          <view class="team-meta">
            <text class="member-count">{{item.memberCount}}人</text>
            <text class="create-time">{{item.createTime}}</text>
          </view>
        </view>
        <view class="team-role {{item.role}}">
          {{item.role === 'main_captain' ? '队长' : item.role === 'vice_captain' ? '副队长' : '队员'}}
        </view>
      </view>
    </scroll-view>
  </view>
</view>
```

#### pages/index/index.js
```javascript
const app = getApp()

Page({
  data: {
    userInfo: null,
    teams: []
  },

  onLoad() {
    this.setData({
      userInfo: app.globalData.userInfo
    })
  },

  onShow() {
    this.loadMyTeams()
  },

  // 加载我的队伍
  loadMyTeams() {
    wx.cloud.callFunction({
      name: 'team',
      data: {
        action: 'getMyTeams'
      },
      success: (res) => {
        if (res.result.success) {
          this.setData({
            teams: res.result.teams
          })
        }
      }
    })
  },

  // 创建队伍
  createTeam() {
    wx.navigateTo({
      url: '/pages/team/create/create'
    })
  },

  // 加入队伍
  joinTeam() {
    wx.navigateTo({
      url: '/pages/team/join/join'
    })
  },

  // 进入队伍详情
  enterTeam(e) {
    const team = e.currentTarget.dataset.team
    wx.navigateTo({
      url: `/pages/team/detail/detail?teamId=${team._id}`
    })
  }
})
```

### 3.3 队伍详情页开发
**时间估计：2天**

#### pages/team/detail/detail.wxml
```html
<view class="container">
  <!-- 队伍信息 -->
  <view class="team-header">
    <view class="team-name">{{team.name}}</view>
    <view class="team-desc">{{team.description}}</view>
    <view class="team-stats">
      <text>{{memberCount}}人参与</text>
      <text>邀请码：{{team.inviteCode}}</text>
    </view>
  </view>

  <!-- 签到点列表 -->
  <view class="checkin-section">
    <view class="section-header">
      <text class="section-title">签到点</text>
      <button wx:if="{{isCaptain}}" class="add-point-btn" bindtap="addCheckinPoint">添加</button>
    </view>
    
    <view class="points-list">
      <view class="point-item" wx:for="{{checkinPoints}}" wx:key="id">
        <view class="point-info">
          <view class="point-name">{{item.name}}</view>
          <view class="point-location">{{item.location.address}}</view>
        </view>
        <button class="checkin-btn" bindtap="performCheckin" data-point="{{item}}">
          签到
        </button>
      </view>
    </view>
  </view>

  <!-- 喊道按钮 -->
  <view class="shout-section">
    <button class="shout-btn" bindtap="performShout">
      <text class="shout-text">喊道报到</text>
    </button>
  </view>

  <!-- 成员列表 -->
  <view class="members-section">
    <view class="section-title">队伍成员</view>
    <scroll-view class="members-list" scroll-y="true">
      <view class="member-item" wx:for="{{members}}" wx:key="openid">
        <image class="member-avatar" src="{{item.avatar}}"></image>
        <view class="member-info">
          <view class="member-name">{{item.nickname}}</view>
          <view class="member-role">{{item.role}}</view>
        </view>
        <button class="call-btn" bindtap="makeCall" data-phone="{{item.phone}}">
          <image class="call-icon" src="/images/icons/phone.png"></image>
        </button>
      </view>
    </scroll-view>
  </view>
</view>
```

#### pages/team/detail/detail.js
```javascript
Page({
  data: {
    teamId: '',
    team: {},
    members: [],
    checkinPoints: [],
    memberCount: 0,
    isCaptain: false
  },

  onLoad(options) {
    this.setData({
      teamId: options.teamId
    })
    this.loadTeamDetail()
  },

  // 加载队伍详情
  loadTeamDetail() {
    wx.cloud.callFunction({
      name: 'team',
      data: {
        action: 'getTeamDetail',
        teamId: this.data.teamId
      },
      success: (res) => {
        if (res.result.success) {
          this.setData({
            team: res.result.team,
            members: res.result.members,
            memberCount: res.result.members.length,
            isCaptain: res.result.isCaptain
          })
        }
      }
    })

    // 加载签到点
    wx.cloud.callFunction({
      name: 'checkin',
      data: {
        action: 'getPoints',
        teamId: this.data.teamId
      },
      success: (res) => {
        if (res.result.success) {
          this.setData({
            checkinPoints: res.result.points
          })
        }
      }
    })
  },

  // 执行签到
  performCheckin(e) {
    const point = e.currentTarget.dataset.point
    
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        wx.cloud.callFunction({
          name: 'checkin',
          data: {
            action: 'checkin',
            teamId: this.data.teamId,
            pointId: point._id,
            userLocation: {
              latitude: res.latitude,
              longitude: res.longitude
            },
            pointLocation: point.location,
            radius: point.radius
          },
          success: (result) => {
            if (result.result.success) {
              wx.showToast({
                title: '签到成功',
                icon: 'success'
              })
            } else {
              wx.showToast({
                title: result.result.error,
                icon: 'none'
              })
            }
          }
        })
      }
    })
  },

  // 喊道报到
  performShout() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        wx.cloud.callFunction({
          name: 'checkin',
          data: {
            action: 'shout',
            teamId: this.data.teamId,
            location: {
              latitude: res.latitude,
              longitude: res.longitude
            }
          },
          success: (result) => {
            if (result.result.success) {
              wx.showToast({
                title: '喊道成功',
                icon: 'success'
              })
            }
          }
        })
      }
    })
  },

  // 一键拨打电话
  makeCall(e) {
    const phone = e.currentTarget.dataset.phone
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      })
    } else {
      wx.showToast({
        title: '该用户未绑定手机号',
        icon: 'none'
      })
    }
  },

  // 添加签到点
  addCheckinPoint() {
    wx.navigateTo({
      url: `/pages/checkin/create-point/create-point?teamId=${this.data.teamId}`
    })
  }
})
```

## 🧪 第四阶段：测试和优化

### 4.1 功能测试
**时间估计：2天**

#### 测试清单：
- [ ] 用户登录和授权
- [ ] 创建队伍功能
- [ ] 加入队伍功能（邀请码）
- [ ] 签到点创建和管理
- [ ] 地理位置签到验证
- [ ] 喊道功能
- [ ] 一键拨打电话
- [ ] 权限控制（队长/队员）
- [ ] 数据实时同步

#### 测试方法：
1. **开发者工具测试**
   ```bash
   # 在微信开发者工具中
   1. 使用模拟器测试基本功能
   2. 使用真机调试测试地理位置功能
   3. 测试多用户场景
   ```

2. **性能测试**
   ```javascript
   // 在云函数中添加性能监控
   console.time('function_execution')
   // 函数逻辑
   console.timeEnd('function_execution')
   ```

### 4.2 样式优化
**时间估计：1天**

#### app.wxss 全局样式
```css
/* 全局样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 按钮样式 */
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 200rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.create-btn {
  background: linear-gradient(135deg, #1aad19, #26d639);
  color: white;
}

.join-btn {
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: white;
}

/* 队伍卡片样式 */
.team-item {
  background: white;
  margin: 20rpx 0;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 签到按钮样式 */
.checkin-btn {
  background: #1aad19;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
}

.shout-btn {
  background: #ff9500;
  color: white;
  border-radius: 50%;
  width: 200rpx;
  height: 200rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(255,149,0,0.3);
}

/* 成员列表样式 */
.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.call-btn {
  background: #007aff;
  color: white;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

## 🚀 第五阶段：部署和发布

### 5.1 云函数部署
**时间估计：0.5天**

#### 操作步骤：
1. **上传云函数**
   ```bash
   # 在微信开发者工具中
   1. 右键点击云函数文件夹
   2. 选择"上传并部署：云端安装依赖"
   3. 等待部署完成
   ```

2. **配置云函数权限**
   ```javascript
   // 在云开发控制台设置云函数触发器和权限
   ```

### 5.2 小程序发布
**时间估计：1天**

#### 操作步骤：
1. **代码审核**
   ```bash
   # 在微信开发者工具中
   1. 点击"上传"
   2. 填写版本号和项目备注
   3. 上传代码包
   ```

2. **提交审核**
   ```bash
   # 在小程序管理后台
   1. 进入版本管理
   2. 选择开发版本
   3. 提交审核
   4. 填写审核信息
   ```

3. **发布上线**
   ```bash
   # 审核通过后
   1. 点击发布
   2. 小程序正式上线
   ```

## 📊 项目时间计划

### 总体时间估计：12天
1. **项目初始化** (1.5天)
2. **数据库和云函数开发** (4天)
3. **前端页面开发** (3.5天)
4. **测试和优化** (3天)
5. **部署和发布** (1.5天)

### 里程碑节点：
- **第3天**：完成环境搭建和数据库设计
- **第7天**：完成核心功能开发
- **第10天**：完成前端页面开发
- **第12天**：项目上线

## 💰 成本估算

### 云开发费用：
- **免费额度**：每月免费额度足够小型项目使用
- **超出部分**：
  - 数据库读写：¥0.015/万次
  - 云函数调用：¥0.0133/万次
  - 云存储：¥0.043/GB/月

### 预计月成本：50-200元（根据用户量）

## 📝 维护和迭代

### 后续功能扩展：
1. **消息推送**：签到提醒、活动通知
2. **数据统计**：签到率统计、活动报告
3. **社交功能**：队伍相册、活动分享
4. **支付功能**：活动费用收取
5. **管理后台**：Web端管理界面

### 运维监控：
- 云函数性能监控
- 数据库性能监控
- 用户行为分析
- 错误日志收集

---

## 📞 技术支持

如在开发过程中遇到问题，可参考：
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- [微信开放社区](https://developers.weixin.qq.com/community/minihome)

**完成此执行计划后，您将拥有一个功能完整的徒步活动签到微信小程序！**
