<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>队员界面 - 徒步签到</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .status-bar {
            background: #000;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .checkin-btn {
            background: linear-gradient(135deg, #2C2C2E 0%, #1C1C1E 100%);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(44, 44, 46, 0.3);
        }
        .checkin-btn:active {
            transform: scale(0.95);
            box-shadow: 0 5px 15px rgba(44, 44, 46, 0.4);
        }
        .response-btn {
            background: linear-gradient(135deg, #6D6D70 0%, #48484A 100%);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(109, 109, 112, 0.3);
        }
        .response-btn:active {
            transform: scale(0.95);
            box-shadow: 0 5px 15px rgba(109, 109, 112, 0.4);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(44, 44, 46, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(44, 44, 46, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(44, 44, 46, 0);
            }
        }
        .notification-card {
            background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        .status-card {
            transition: all 0.2s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS状态栏 -->
    <div class="status-bar px-6 py-2 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="bg-white px-6 py-4 border-b border-gray-100 flex items-center justify-between">
        <div class="flex items-center">
            <i class="fas fa-arrow-left text-gray-600 mr-4" onclick="goBack()"></i>
            <h1 class="text-xl font-bold text-gray-900">队员界面</h1>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-user text-gray-800"></i>
            <span class="text-sm text-gray-600">王小明</span>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="px-6 py-6 pb-24">
        <!-- 活动信息 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-sm" id="activityInfo">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">活动信息</h2>
                    <p class="text-gray-600 text-sm mt-1">香山徒步活动</p>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <i class="fas fa-hiking text-green-600 text-xl"></i>
                </div>
            </div>
            
            <div class="flex items-center space-x-2 mb-3">
                <i class="fas fa-users text-gray-500"></i>
                <span class="text-sm text-gray-600">队长：张队长</span>
            </div>
            
            <div class="flex items-center space-x-2">
                <i class="fas fa-calendar text-gray-500"></i>
                <span class="text-sm text-gray-600">今天 09:00 开始</span>
            </div>
        </div>
        
        <!-- 队长喊话通知 -->
        <div id="shoutNotification" class="notification-card rounded-2xl p-6 mb-6 text-white" style="display: none;">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-xl font-bold">队长喊话</h2>
                    <p class="text-white/80 text-sm mt-1">张队长正在清点人数</p>
                </div>
                <div class="bg-white/20 p-3 rounded-full">
                    <i class="fas fa-bullhorn text-2xl"></i>
                </div>
            </div>
            
            <div class="flex items-center space-x-2 mb-3">
                <i class="fas fa-clock text-white/80"></i>
                <span class="text-sm text-white/80">09:20 发起</span>
            </div>
            
            <button class="response-btn w-full py-4 rounded-xl text-white font-semibold text-lg" onclick="respondToShout()">
                <i class="fas fa-hand-paper mr-2"></i>
                我到了！
            </button>
        </div>
        
        <!-- 当前位置信息 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">集合点信息</h2>
                    <p class="text-gray-600 text-sm mt-1">香山公园东门</p>
                </div>
                <div class="bg-gray-100 p-3 rounded-full">
                    <i class="fas fa-map-marker-alt text-gray-600 text-xl"></i>
                </div>
            </div>
            
            <div class="flex items-center space-x-2 mb-3">
                <i class="fas fa-clock text-gray-500"></i>
                <span class="text-sm text-gray-600">集合时间：09:00 - 09:30</span>
            </div>
            
            <div class="flex items-center space-x-2">
                <i class="fas fa-location-arrow text-gray-500"></i>
                <span class="text-sm text-gray-600">距离集合点：约50米</span>
            </div>
        </div>
        
        <!-- 签到按钮 -->
        <div class="text-center mb-6">
            <button class="checkin-btn w-48 h-48 rounded-full text-white pulse-animation" onclick="checkIn()">
                <div class="flex flex-col items-center">
                    <i class="fas fa-check-circle text-4xl mb-2"></i>
                    <span class="text-xl font-bold">我已上车</span>
                    <span class="text-sm opacity-80">到达后点击签到</span>
                </div>
            </button>
        </div>
        
        <!-- 我的状态 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">我的状态</h3>
            
            <div class="status-card flex items-center space-x-3 p-4 bg-gray-100 rounded-xl">
                <div class="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold">王</span>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-gray-900">王小明</div>
                    <div class="text-sm text-gray-600">等待签到</div>
                </div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-clock text-gray-500"></i>
                    <span class="text-sm text-gray-600">未签到</span>
                </div>
            </div>
        </div>
        
        <!-- 团队信息 -->
        <div class="bg-white rounded-2xl p-6 mt-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">团队信息</h3>
            
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center p-3 bg-gray-50 rounded-xl">
                    <div class="text-xl font-bold text-gray-800">15</div>
                    <div class="text-xs text-gray-600 mt-1">总人数</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-xl">
                    <div class="text-xl font-bold text-green-600">8</div>
                    <div class="text-xs text-green-600 mt-1">已签到</div>
                </div>
                <div class="text-center p-3 bg-gray-100 rounded-xl">
                    <div class="text-xl font-bold text-gray-600">3</div>
                    <div class="text-xs text-gray-600 mt-1">我到了</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-4">
        <button class="w-full bg-gray-800 text-white py-4 rounded-xl font-medium text-lg" onclick="checkIn()">
            <i class="fas fa-check-circle mr-2"></i>
            我已上车
        </button>
    </div>
    
    <script>
        function goBack() {
            window.location.href = 'role-select.html';
        }
        
        function checkIn() {
            alert('签到成功！');
            updateMemberStatus('checked_in');
        }
        
        function joinActivity() {
            window.location.href = 'join-activity.html';
        }
        
        function respondToShout() {
            alert('已响应队长喊话：我到了！');
            document.getElementById('shoutNotification').style.display = 'none';
            
            // 更新用户状态
            updateMemberStatus('arrived');
        }
        
        function updateMemberStatus(status) {
            const activityJoined = localStorage.getItem('activityJoined');
            const activityCode = localStorage.getItem('activityCode');
            
            if (activityJoined && activityCode) {
                // 在实际应用中，这里应该向服务器更新状态
                localStorage.setItem('memberStatus', status);
                loadActivityInfo();
            }
        }
        
        function loadActivityInfo() {
            const activityJoined = localStorage.getItem('activityJoined');
            const activityCode = localStorage.getItem('activityCode');
            const memberStatus = localStorage.getItem('memberStatus') || 'no_response';
            
            if (activityJoined && activityCode) {
                // 显示已加入的活动信息
                const statusText = {
                    'checked_in': '已签到',
                    'arrived': '已到达',
                    'no_response': '未响应'
                };
                
                const statusColor = {
                    'checked_in': 'green',
                    'arrived': 'blue', 
                    'no_response': 'gray'
                };
                
                document.getElementById('activityInfo').innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">徒步活动</h2>
                            <p class="text-gray-600 text-sm mt-1">活动码：${activityCode}</p>
                        </div>
                        <div class="bg-${statusColor[memberStatus]}-100 p-3 rounded-full">
                            <i class="fas fa-hiking text-${statusColor[memberStatus]}-600 text-xl"></i>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2 mb-3">
                        <i class="fas fa-user-check text-gray-500"></i>
                        <span class="text-sm text-gray-600">状态：${statusText[memberStatus]}</span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-calendar text-gray-500"></i>
                        <span class="text-sm text-gray-600">今天 09:00 开始</span>
                    </div>
                `;
            } else {
                // 显示未加入活动的状态
                document.getElementById('activityInfo').innerHTML = `
                    <div class="text-center py-8">
                        <div class="bg-gray-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-key text-gray-500 text-xl"></i>
                        </div>
                        <h3 class="font-medium text-gray-900 mb-2">暂未加入活动</h3>
                        <p class="text-gray-600 text-sm mb-4">请通过活动口令加入徒步活动</p>
                        <button class="bg-gray-800 text-white px-6 py-2 rounded-xl text-sm font-medium" onclick="joinActivity()">
                            <i class="fas fa-key mr-2"></i>输入口令加入
                        </button>
                    </div>
                `;
            }
        }
        
        function updateStatus(status, icon, color) {
            const statusCard = document.querySelector('.status-card');
            const statusText = statusCard.querySelector('.text-sm');
            const statusIcon = statusCard.querySelector('.fas');
            
            statusText.textContent = status;
            statusIcon.className = `fas fa-${icon} text-${color}-500`;
        }
        
        // 页面加载时加载活动信息
        document.addEventListener('DOMContentLoaded', function() {
            loadActivityInfo();
        });
        
        // 模拟接收队长喊话
        setTimeout(() => {
            document.getElementById('shoutNotification').style.display = 'block';
        }, 3000);
    </script>
</body>
</html>