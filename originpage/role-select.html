<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色选择 - 徒步签到</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .status-bar {
            background: #000;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .role-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #2C2C2E 0%, #1C1C1E 100%);
        }
        .role-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        .role-card:active {
            transform: translateY(-4px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS状态栏 -->
    <div class="status-bar px-6 py-2 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="px-6 py-12">
        <!-- 标题区域 -->
        <div class="text-center mb-12">
            <div class="w-20 h-20 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-hiking text-white text-3xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">徒步签到</h1>
            <p class="text-gray-600">请选择您的身份</p>
        </div>
        
        <!-- 角色选择卡片 -->
        <div class="space-y-6">
            <!-- 队长卡片 -->
            <div class="role-card rounded-2xl p-8 text-white cursor-pointer" onclick="selectRole('captain')">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-crown text-3xl"></i>
                    </div>
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold mb-2">我是队长</h2>
                        <p class="text-white/80 text-sm">创建管理活动、队员签到管理</p>
                    </div>
                    <i class="fas fa-chevron-right text-white/60"></i>
                </div>
            </div>
            
            <!-- 队员卡片 -->
            <div class="role-card rounded-2xl p-8 text-white cursor-pointer" onclick="selectRole('member')">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-3xl"></i>
                    </div>
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold mb-2">我是队员</h2>
                        <p class="text-white/80 text-sm">参与活动签到、响应队长喊话</p>
                    </div>
                    <i class="fas fa-chevron-right text-white/60"></i>
                </div>
            </div>
            
            <!-- 加入活动卡片 -->
            <div class="role-card rounded-2xl p-8 text-white cursor-pointer" onclick="selectRole('join')">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-key text-3xl"></i>
                    </div>
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold mb-2">加入活动</h2>
                        <p class="text-white/80 text-sm">通过分享口令、快速加入活动</p>
                    </div>
                    <i class="fas fa-chevron-right text-white/60"></i>
                </div>
            </div>
        </div>
        
        <!-- 说明文字 -->
        <div class="mt-12 text-center">
            <p class="text-gray-500 text-sm">根据您的身份，系统将为您提供相应的功能</p>
        </div>
    </div>
    
    <script>
        function selectRole(role) {
            if (role === 'captain') {
                window.location.href = 'captain-dashboard.html';
            } else if (role === 'member') {
                window.location.href = 'member-dashboard.html';
            } else if (role === 'join') {
                window.location.href = 'join-activity.html';
            }
        }
    </script>
</body>
</html>