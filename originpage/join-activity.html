<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加入活动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            background: rgba(0,0,0,0.3);
            font-size: 14px;
            font-weight: 600;
        }
        
        .nav-bar {
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #333;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            margin-right: 16px;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .main-content {
            flex: 1;
            padding: 40px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .join-form {
            width: 100%;
            max-width: 320px;
            text-align: center;
        }
        
        .form-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
        }
        
        .form-icon i {
            font-size: 36px;
            color: #fff;
        }
        
        .form-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .form-subtitle {
            color: #999;
            margin-bottom: 40px;
            line-height: 1.5;
        }
        
        .input-group {
            margin-bottom: 30px;
        }
        
        .input-label {
            display: block;
            font-size: 16px;
            margin-bottom: 12px;
            text-align: left;
        }
        
        .code-input {
            width: 100%;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border: 2px solid #333;
            border-radius: 12px;
            color: #fff;
            font-size: 24px;
            text-align: center;
            letter-spacing: 8px;
            font-weight: 600;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .code-input:focus {
            border-color: #666;
            background: rgba(255,255,255,0.15);
        }
        
        .code-input::placeholder {
            color: #666;
            letter-spacing: 4px;
        }
        
        .join-btn {
            width: 100%;
            height: 56px;
            background: #fff;
            color: #000;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .join-btn:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
        }
        
        .join-btn:disabled {
            background: #333;
            color: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .help-text {
            color: #999;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .help-text a {
            color: #fff;
            text-decoration: none;
        }
        
        .error-message {
            color: #ff4444;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }
        
        .success-message {
            color: #44ff44;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>22:26</span>
            <div>
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="nav-title">加入活动</div>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <div class="join-form">
                <div class="form-icon">
                    <i class="fas fa-key"></i>
                </div>
                
                <h1 class="form-title">输入活动口令</h1>
                <p class="form-subtitle">请输入队长分享的6位数字口令<br>即可快速加入徒步活动</p>
                
                <div class="input-group">
                    <label class="input-label">活动口令</label>
                    <input type="text" class="code-input" id="activityCode" placeholder="000000" maxlength="6" oninput="validateCode()">
                    <div class="error-message" id="errorMessage">口令格式不正确，请输入6位数字</div>
                    <div class="success-message" id="successMessage">口令验证成功！</div>
                </div>
                
                <button class="join-btn" id="joinBtn" onclick="joinActivity()" disabled>
                    加入活动
                </button>
                
                <p class="help-text">
                    没有口令？请联系活动队长获取<br>
                    或 <a href="role-select.html">返回选择其他方式</a>
                </p>
            </div>
        </div>
    </div>
    
    <script>
        function goBack() {
            window.location.href = 'role-select.html';
        }
        
        function validateCode() {
            const codeInput = document.getElementById('activityCode');
            const joinBtn = document.getElementById('joinBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            const code = codeInput.value;
            
            // 只允许输入数字
            codeInput.value = code.replace(/[^0-9]/g, '');
            
            // 验证长度
            if (codeInput.value.length === 6) {
                joinBtn.disabled = false;
                errorMessage.style.display = 'none';
                successMessage.style.display = 'block';
            } else {
                joinBtn.disabled = true;
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';
            }
        }
        
        function joinActivity() {
            const code = document.getElementById('activityCode').value;
            
            if (code.length !== 6) {
                document.getElementById('errorMessage').style.display = 'block';
                return;
            }
            
            // 检查活动是否存在
            const activities = JSON.parse(localStorage.getItem('activities') || '{}');
            
            if (activities[code]) {
                // 保存加入的活动信息
                localStorage.setItem('userRole', 'member');
                localStorage.setItem('activityCode', code);
                localStorage.setItem('activityJoined', 'true');
                localStorage.setItem('memberStatus', 'no_response');
                
                alert('成功加入活动：' + activities[code].name);
                window.location.href = 'member-dashboard.html';
            } else {
                document.getElementById('errorMessage').textContent = '活动口令错误或活动不存在，请重新输入';
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
            }
        }
        
        // 自动聚焦输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('activityCode').focus();
        });
    </script>
</body>
</html>