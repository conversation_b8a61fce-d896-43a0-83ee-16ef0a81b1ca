<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建活动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            background: rgba(0,0,0,0.3);
            font-size: 14px;
            font-weight: 600;
        }
        
        .nav-bar {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 1px solid #333;
        }
        
        .nav-left {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            margin-right: 16px;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .save-btn {
            background: #fff;
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }
        
        .main-content {
            padding: 20px;
            padding-bottom: 100px;
        }
        
        .form-section {
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #333;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            color: #999;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            margin-bottom: 8px;
            color: #ccc;
        }
        
        .form-input {
            width: 100%;
            height: 48px;
            background: rgba(255,255,255,0.1);
            border: 1px solid #444;
            border-radius: 8px;
            color: #fff;
            font-size: 16px;
            padding: 0 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            border-color: #666;
            background: rgba(255,255,255,0.15);
        }
        
        .form-textarea {
            width: 100%;
            min-height: 80px;
            background: rgba(255,255,255,0.1);
            border: 1px solid #444;
            border-radius: 8px;
            color: #fff;
            font-size: 16px;
            padding: 12px 16px;
            outline: none;
            resize: vertical;
            font-family: inherit;
        }
        
        .form-textarea:focus {
            border-color: #666;
            background: rgba(255,255,255,0.15);
        }
        
        .file-upload {
            position: relative;
            display: block;
            width: 100%;
            height: 120px;
            background: rgba(255,255,255,0.05);
            border: 2px dashed #444;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload:hover {
            border-color: #666;
            background: rgba(255,255,255,0.1);
        }
        
        .file-upload input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
        }
        
        .file-upload-icon {
            font-size: 32px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .file-upload-text {
            color: #999;
            font-size: 14px;
        }
        
        .file-info {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
            display: none;
        }
        
        .file-info.show {
            display: block;
        }
        
        .location-input {
            position: relative;
        }
        
        .location-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: #fff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .datetime-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .code-display {
            background: rgba(255,255,255,0.1);
            border: 1px solid #444;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .code-number {
            font-size: 32px;
            font-weight: 700;
            letter-spacing: 4px;
            margin-bottom: 8px;
        }
        
        .code-label {
            color: #999;
            font-size: 14px;
        }
        
        .create-btn {
            width: 100%;
            height: 56px;
            background: #fff;
            color: #000;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 30px;
            transition: all 0.3s ease;
        }
        
        .create-btn:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
        }
        
        .member-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 12px;
        }
        
        .member-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #333;
        }
        
        .member-item:last-child {
            border-bottom: none;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .member-phone {
            color: #999;
            font-size: 12px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>22:26</span>
            <div>
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-left">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="nav-title">创建活动</div>
            </div>
            <button class="save-btn" onclick="saveActivity()">保存</button>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    基本信息
                </div>
                
                <div class="form-group">
                    <label class="form-label">活动名称</label>
                    <input type="text" class="form-input" id="activityName" placeholder="请输入活动名称">
                </div>
                
                <div class="form-group">
                    <label class="form-label">活动描述</label>
                    <textarea class="form-textarea" id="activityDesc" placeholder="请输入活动描述（可选）"></textarea>
                </div>
            </div>
            
            <!-- 队员信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-users"></i>
                    队员信息
                </div>
                
                <div class="form-group">
                    <label class="form-label">导入Excel文件（包含姓名和电话）</label>
                    <label class="file-upload">
                        <input type="file" accept=".xlsx,.xls" onchange="handleFileUpload(event)">
                        <div class="file-upload-content">
                            <i class="fas fa-file-excel file-upload-icon"></i>
                            <div class="file-upload-text">点击选择Excel文件<br>格式：姓名 | 电话号码</div>
                        </div>
                    </label>
                    <div class="file-info" id="fileInfo">
                        <div id="fileName"></div>
                        <div id="memberCount"></div>
                    </div>
                </div>
                
                <div class="member-list" id="memberList"></div>
            </div>
            
            <!-- 地点信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-map-marker-alt"></i>
                    地点信息
                </div>
                
                <div class="form-group">
                    <label class="form-label">集合地点</label>
                    <div class="location-input">
                        <input type="text" class="form-input" id="meetingLocation" placeholder="请输入集合地点">
                        <button class="location-btn" onclick="getCurrentLocation('meetingLocation')">
                            <i class="fas fa-crosshairs"></i> 定位
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">目的地</label>
                    <div class="location-input">
                        <input type="text" class="form-input" id="destination" placeholder="请输入目的地">
                        <button class="location-btn" onclick="getCurrentLocation('destination')">
                            <i class="fas fa-crosshairs"></i> 定位
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 时间信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-clock"></i>
                    时间安排
                </div>
                
                <div class="form-group">
                    <label class="form-label">集合时间</label>
                    <div class="datetime-group">
                        <input type="date" class="form-input" id="meetingDate">
                        <input type="time" class="form-input" id="meetingTime">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">预计返程时间</label>
                    <div class="datetime-group">
                        <input type="date" class="form-input" id="returnDate">
                        <input type="time" class="form-input" id="returnTime">
                    </div>
                </div>
            </div>
            
            <!-- 活动口令 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-key"></i>
                    活动口令
                </div>
                
                <div class="code-display">
                    <div class="code-number" id="activityCode">123456</div>
                    <div class="code-label">队员可通过此口令加入活动</div>
                </div>
            </div>
            
            <button class="create-btn" onclick="createActivity()">创建活动</button>
        </div>
    </div>
    
    <script>
        let memberData = [];
        
        function goBack() {
            window.location.href = 'captain-dashboard.html';
        }
        
        function generateActivityCode() {
            return Math.floor(100000 + Math.random() * 900000).toString();
        }
        
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {type: 'array'});
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                    const jsonData = XLSX.utils.sheet_to_json(firstSheet, {header: 1});
                    
                    memberData = [];
                    for (let i = 1; i < jsonData.length; i++) { // 跳过标题行
                        const row = jsonData[i];
                        if (row[0] && row[1]) {
                            memberData.push({
                                name: row[0].toString().trim(),
                                phone: row[1].toString().trim()
                            });
                        }
                    }
                    
                    displayFileInfo(file.name, memberData.length);
                    displayMemberList();
                } catch (error) {
                    alert('文件解析失败，请检查Excel格式');
                }
            };
            reader.readAsArrayBuffer(file);
        }
        
        function displayFileInfo(fileName, memberCount) {
            const fileInfo = document.getElementById('fileInfo');
            const fileNameEl = document.getElementById('fileName');
            const memberCountEl = document.getElementById('memberCount');
            
            fileNameEl.textContent = `文件：${fileName}`;
            memberCountEl.textContent = `成功导入 ${memberCount} 名队员`;
            fileInfo.classList.add('show');
        }
        
        function displayMemberList() {
            const memberList = document.getElementById('memberList');
            memberList.innerHTML = '';
            
            memberData.forEach((member, index) => {
                const memberItem = document.createElement('div');
                memberItem.className = 'member-item';
                memberItem.innerHTML = `
                    <div class="member-info">
                        <div class="member-name">${member.name}</div>
                        <div class="member-phone">${member.phone}</div>
                    </div>
                `;
                memberList.appendChild(memberItem);
            });
        }
        
        function getCurrentLocation(inputId) {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        document.getElementById(inputId).value = `纬度: ${lat.toFixed(6)}, 经度: ${lng.toFixed(6)}`;
                    },
                    function(error) {
                        alert('定位失败，请手动输入地点');
                    }
                );
            } else {
                alert('浏览器不支持定位功能');
            }
        }
        
        function saveActivity() {
            // 保存草稿到localStorage
            const activityData = {
                name: document.getElementById('activityName').value,
                description: document.getElementById('activityDesc').value,
                meetingLocation: document.getElementById('meetingLocation').value,
                destination: document.getElementById('destination').value,
                meetingDate: document.getElementById('meetingDate').value,
                meetingTime: document.getElementById('meetingTime').value,
                returnDate: document.getElementById('returnDate').value,
                returnTime: document.getElementById('returnTime').value,
                members: memberData,
                code: document.getElementById('activityCode').textContent
            };
            
            localStorage.setItem('activityDraft', JSON.stringify(activityData));
            alert('活动已保存为草稿');
        }
        
        function createActivity() {
            const activityName = document.getElementById('activityName').value;
            const meetingLocation = document.getElementById('meetingLocation').value;
            const meetingDate = document.getElementById('meetingDate').value;
            const meetingTime = document.getElementById('meetingTime').value;
            
            if (!activityName || !meetingLocation || !meetingDate || !meetingTime) {
                alert('请填写必要信息：活动名称、集合地点、集合时间');
                return;
            }
            
            if (memberData.length === 0) {
                alert('请导入队员信息');
                return;
            }
            
            const activityCode = document.getElementById('activityCode').textContent;
            
            const activityData = {
                id: Date.now().toString(),
                name: activityName,
                description: document.getElementById('activityDesc').value,
                meetingLocation: meetingLocation,
                destination: document.getElementById('destination').value,
                meetingDate: meetingDate,
                meetingTime: meetingTime,
                returnDate: document.getElementById('returnDate').value,
                returnTime: document.getElementById('returnTime').value,
                members: memberData,
                code: activityCode,
                createdAt: new Date().toISOString(),
                status: 'active',
                memberStatus: {} // 用于跟踪每个成员的状态
            };
            
            // 保存到活动列表（使用活动码作为key）
            const activities = JSON.parse(localStorage.getItem('activities') || '{}');
            activities[activityCode] = activityData;
            localStorage.setItem('activities', JSON.stringify(activities));
            
            // 保存当前活动和用户角色
            localStorage.setItem('currentActivity', JSON.stringify(activityData));
            localStorage.setItem('userRole', 'captain');
            localStorage.setItem('currentActivityCode', activityCode);
            
            alert('活动创建成功！\n活动名称：' + activityName + '\n活动口令：' + activityCode + '\n请将口令分享给队员');
            window.location.href = 'captain-dashboard.html';
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 生成活动口令
            document.getElementById('activityCode').textContent = generateActivityCode();
            
            // 设置默认日期时间
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            
            document.getElementById('meetingDate').value = today;
            document.getElementById('meetingTime').value = currentTime;
            document.getElementById('returnDate').value = today;
            
            // 加载草稿
            const draft = localStorage.getItem('activityDraft');
            if (draft) {
                const data = JSON.parse(draft);
                document.getElementById('activityName').value = data.name || '';
                document.getElementById('activityDesc').value = data.description || '';
                document.getElementById('meetingLocation').value = data.meetingLocation || '';
                document.getElementById('destination').value = data.destination || '';
                document.getElementById('meetingDate').value = data.meetingDate || today;
                document.getElementById('meetingTime').value = data.meetingTime || currentTime;
                document.getElementById('returnDate').value = data.returnDate || today;
                document.getElementById('returnTime').value = data.returnTime || '';
                
                if (data.members && data.members.length > 0) {
                    memberData = data.members;
                    displayFileInfo('已保存的队员信息', memberData.length);
                    displayMemberList();
                }
                
                if (data.code) {
                    document.getElementById('activityCode').textContent = data.code;
                }
            }
        });
    </script>
</body>
</html>