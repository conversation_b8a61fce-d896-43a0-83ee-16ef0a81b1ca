<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>队长控制台 - 徒步签到</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .status-bar {
            background: #000;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .shout-btn {
            background: linear-gradient(135deg, #2C2C2E 0%, #1C1C1E 100%);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(44, 44, 46, 0.3);
        }
        .shout-btn:active {
            transform: scale(0.95);
            box-shadow: 0 5px 15px rgba(44, 44, 46, 0.4);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(44, 44, 46, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(44, 44, 46, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(44, 44, 46, 0);
            }
        }
        .member-card {
            transition: all 0.2s ease;
        }
        .member-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS状态栏 -->
    <div class="status-bar px-6 py-2 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="bg-white px-6 py-4 border-b border-gray-100 flex items-center justify-between">
        <div class="flex items-center">
            <i class="fas fa-arrow-left text-gray-600 mr-4" onclick="goBack()"></i>
            <h1 class="text-xl font-bold text-gray-900">队长控制台</h1>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-crown text-gray-800"></i>
            <span class="text-sm text-gray-600">张队长</span>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="px-6 py-6 pb-24">
        <!-- 活动管理 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">活动管理</h2>
                <button class="bg-gray-800 text-white px-4 py-2 rounded-xl text-sm font-medium" onclick="createActivity()">
                    <i class="fas fa-plus mr-2"></i>创建活动
                </button>
            </div>
            
            <div id="currentActivity" class="bg-gray-50 rounded-xl p-4">
                <div class="text-center">
                    <h3 class="font-medium text-gray-900 mb-1">暂无活动</h3>
                    <p class="text-sm text-gray-600">点击创建活动开始管理队员</p>
                    <div class="mt-2 text-lg font-bold text-gray-400">--</div>
                </div>
            </div>
        </div>
        
        <!-- 快速统计 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">实时统计</h2>
            
            <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="text-center p-4 bg-green-50 rounded-xl">
                    <div id="checkedInCount" class="text-2xl font-bold text-green-600">0</div>
                    <div class="text-sm text-green-600 mt-1">已签到</div>
                </div>
                <div class="text-center p-4 bg-gray-100 rounded-xl">
                    <div id="arrivedCount" class="text-2xl font-bold text-gray-600">0</div>
                    <div class="text-sm text-gray-600 mt-1">我到了</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded-xl">
                    <div id="noResponseCount" class="text-2xl font-bold text-red-600">0</div>
                    <div class="text-sm text-red-600 mt-1">未响应</div>
                </div>
            </div>
            
            <div class="bg-gray-100 rounded-full h-3 mb-2">
                <div class="bg-gray-800 h-3 rounded-full" style="width: 73%"></div>
            </div>
            <p class="text-center text-sm text-gray-500">签到进度 11/15 (73%)</p>
        </div>
        
        <!-- 发起喊话按钮 -->
        <div class="text-center mb-6">
            <button class="shout-btn w-48 h-48 rounded-full text-white pulse-animation" onclick="startShout()">
                <div class="flex flex-col items-center">
                    <i class="fas fa-bullhorn text-4xl mb-2"></i>
                    <span class="text-xl font-bold">发起喊话</span>
                    <span class="text-sm opacity-80">清点人数</span>
                </div>
            </button>
        </div>
        
        <!-- 队员状态列表 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">队员状态</h3>
                <span class="text-sm text-gray-500">共15人</span>
            </div>
            
            <div class="space-y-3">
                <!-- 已签到队员 -->
                <div class="member-card flex items-center space-x-3 p-3 bg-green-50 rounded-xl">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">李</span>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">李副队</div>
                        <div class="text-xs text-gray-600">09:12 签到</div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check-circle text-green-500"></i>
                        <span class="text-xs text-green-600 font-medium">已签到</span>
                    </div>
                </div>
                
                <!-- 我到了队员 -->
                <div class="member-card flex items-center space-x-3 p-3 bg-gray-100 rounded-xl">
                    <div class="w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">王</span>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">王小明</div>
                        <div class="text-xs text-gray-600">09:18 我到了</div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-bullhorn text-gray-600"></i>
                        <span class="text-xs text-gray-600 font-medium">我到了</span>
                    </div>
                </div>
                
                <!-- 未响应队员 -->
                <div class="member-card flex items-center space-x-3 p-3 bg-red-50 rounded-xl">
                    <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">陈</span>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">陈小华</div>
                        <div class="text-xs text-gray-400">未响应</div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-clock text-red-500"></i>
                        <span class="text-xs text-red-600 font-medium">未响应</span>
                    </div>
                </div>
                
                <div class="text-center py-2">
                    <button class="text-sm text-gray-500" onclick="showAllMembers()">查看全部队员 (15)</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-4">
        <div class="flex space-x-3">
            <button class="flex-1 bg-gray-800 text-white py-3 rounded-xl font-medium" onclick="startShout()">
                <i class="fas fa-bullhorn mr-2"></i>
                发起喊话
            </button>
            <button class="flex-1 bg-gray-200 text-gray-800 py-3 rounded-xl font-medium" onclick="exportData()">
                <i class="fas fa-download mr-2"></i>
                导出数据
            </button>
        </div>
    </div>
    
    <script>
        function goBack() {
            window.location.href = 'role-select.html';
        }
        
        function startShout() {
            alert('喊话已发起！队员将收到通知并可以响应"我到了"');
            // 这里可以添加实际的喊话逻辑
        }
        
        function showAllMembers() {
            alert('显示全部队员列表');
        }
        
        function exportData() {
            alert('导出签到数据');
        }
        
        function createActivity() {
             window.location.href = 'create-activity.html';
         }
         
         function loadCurrentActivity() {
             const currentActivity = localStorage.getItem('currentActivity');
             if (currentActivity) {
                 const activity = JSON.parse(currentActivity);
                 document.getElementById('currentActivity').innerHTML = `
                     <div class="flex items-center justify-between">
                         <div>
                             <h3 class="font-medium text-gray-900">${activity.name}</h3>
                             <p class="text-sm text-gray-600">${activity.meetingDate} ${activity.meetingTime}</p>
                         </div>
                         <div class="text-center">
                             <div class="text-lg font-bold text-gray-800">${activity.code}</div>
                             <div class="text-xs text-gray-500">活动码</div>
                         </div>
                     </div>
                 `;
                 
                 // 更新统计数据
                 updateStats(activity.members);
             }
         }
         
         function updateStats(members) {
             if (!members) return;
             
             let checkedIn = 0;
             let arrived = 0;
             let noResponse = 0;
             
             members.forEach(member => {
                 const status = member.status || 'no_response';
                 switch(status) {
                     case 'checked_in':
                         checkedIn++;
                         break;
                     case 'arrived':
                         arrived++;
                         break;
                     default:
                         noResponse++;
                 }
             });
             
             document.getElementById('checkedInCount').textContent = checkedIn;
             document.getElementById('arrivedCount').textContent = arrived;
             document.getElementById('noResponseCount').textContent = noResponse;
         }
         
         // 页面加载时加载当前活动
         document.addEventListener('DOMContentLoaded', function() {
             loadCurrentActivity();
         });
    </script>
</body>
</html>