# 徒步签到小程序开发说明

## 项目概述

这是一个基于微信小程序的徒步活动签到管理系统，帮助队长高效管理队员，确保活动安全有序进行。

## 功能特性

### 已实现功能

✅ **角色选择系统**
- 队长/队员身份选择
- 加入活动功能
- 角色切换支持

✅ **队长控制台**
- 活动概览和统计
- 实时签到进度
- 发起喊话功能
- 成员管理入口
- 数据导出功能

✅ **队员界面**
- 活动信息展示
- 一键签到功能
- 喊话响应
- 团队统计查看

✅ **活动管理**
- 创建活动（基础框架）
- 通过口令加入活动
- 活动信息预览

✅ **基础架构**
- 云开发环境配置
- 模拟数据系统
- API接口封装
- 统一UI设计

### 待完善功能

🔄 **签到系统**
- GPS位置验证
- 签到点管理
- 签到记录存储

🔄 **喊话系统**
- 实时消息推送
- 响应状态管理
- 历史记录查看

🔄 **成员管理**
- Excel批量导入
- 权限分配
- 成员信息编辑

🔄 **云开发后端**
- 云函数实现
- 数据库设计
- 用户认证系统

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生开发
- **样式**: WXSS + 现代化设计系统
- **状态管理**: 页面级状态管理
- **API**: 统一接口封装

### 后端技术栈
- **云开发**: 微信云开发
- **数据库**: 云数据库
- **存储**: 云存储
- **函数**: 云函数

### 设计规范
- **色彩**: 现代化蓝绿渐变主题
- **组件**: 统一的卡片和按钮设计
- **交互**: 流畅的动画和反馈
- **适配**: 响应式布局设计

## 项目结构

```
hiking-record/
├── miniprogram/                 # 小程序前端代码
│   ├── app.js                  # 应用入口
│   ├── app.json                # 应用配置
│   ├── app.wxss                # 全局样式
│   ├── pages/                  # 页面目录
│   │   ├── role-select/        # 角色选择页
│   │   ├── captain-dashboard/  # 队长控制台
│   │   ├── member-dashboard/   # 队员界面
│   │   ├── join-activity/      # 加入活动
│   │   └── create-activity/    # 创建活动
│   ├── components/             # 组件目录
│   ├── utils/                  # 工具函数
│   │   ├── api.js             # API接口
│   │   └── mockData.js        # 模拟数据
│   └── images/                 # 图片资源
├── cloudfunctions/             # 云函数目录
├── PRD.md                      # 产品需求文档
└── project.config.json         # 项目配置
```

## 开发环境搭建

### 1. 安装微信开发者工具

1. 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 使用微信扫码登录

### 2. 导入项目

1. 打开微信开发者工具
2. 选择「导入项目」
3. 选择项目目录：`/Users/<USER>/Desktop/hiking-record`
4. 填写 AppID（测试可使用测试号）
5. 点击「导入」

### 3. 配置云开发

1. 在微信开发者工具中点击「云开发」
2. 开通云开发服务
3. 创建云环境
4. 更新 `app.js` 中的环境ID

```javascript
// app.js
env: "your-cloud-env-id", // 替换为你的云环境ID
```

## 使用说明

### 队长使用流程

1. **选择身份**: 打开小程序，选择「我是队长」
2. **创建活动**: 在控制台点击「创建活动」，填写活动信息
3. **管理队员**: 添加队员信息，设置权限
4. **分享口令**: 将生成的6位口令分享给队员
5. **实时监控**: 查看签到进度和队员状态
6. **发起喊话**: 需要时发起全员喊话

### 队员使用流程

1. **加入活动**: 选择「加入活动」，输入队长提供的口令
2. **查看信息**: 查看活动详情和集合信息
3. **及时签到**: 到达指定地点后点击「我已上车」
4. **响应喊话**: 收到队长喊话后及时响应

## 核心功能说明

### 角色管理
- 支持队长、副队长、队员三种角色
- 可在个人中心切换角色
- 不同角色有不同的功能权限

### 签到系统
- 基于GPS位置的签到验证
- 支持设置签到半径
- 实时更新签到状态

### 喊话功能
- 队长可发起全员喊话
- 队员收到通知后可快速响应
- 实时统计响应情况

### 数据管理
- 支持Excel批量导入队员
- 可导出签到记录
- 实时数据同步

## 开发注意事项

### 1. 代码规范
- 遵循微信小程序开发规范
- 使用ES6+语法
- 统一的命名规范和注释

### 2. 性能优化
- 合理使用setData
- 图片资源优化
- 避免频繁的API调用

### 3. 用户体验
- 提供清晰的操作反馈
- 优雅的错误处理
- 流畅的页面切换

### 4. 安全考虑
- 用户数据加密传输
- 合理的权限控制
- 防止恶意操作

## 调试和测试

### 1. 本地调试
- 使用微信开发者工具的调试功能
- 查看控制台日志
- 使用模拟器测试不同设备

### 2. 真机测试
- 使用预览功能在真机测试
- 测试GPS定位功能
- 验证网络请求

### 3. 云函数调试
- 在云开发控制台查看日志
- 使用本地调试功能
- 测试数据库操作

## 部署发布

### 1. 代码审查
- 检查代码质量
- 清理调试代码
- 优化性能

### 2. 提交审核
- 在微信开发者工具中上传代码
- 在微信公众平台提交审核
- 等待审核通过

### 3. 发布上线
- 审核通过后发布
- 监控线上运行状态
- 收集用户反馈

## 常见问题

### Q: 如何获取位置权限？
A: 在app.json中配置位置权限，并在使用时引导用户授权。

### Q: 云开发环境如何配置？
A: 在微信开发者工具中开通云开发，创建环境后更新app.js中的环境ID。

### Q: 如何处理网络请求失败？
A: 在API封装中添加重试机制和错误处理，给用户友好的提示。

### Q: 如何优化小程序性能？
A: 减少setData频率，优化图片资源，使用分包加载等。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 这是一个开发版本，部分功能仍在完善中。请在生产环境使用前进行充分测试。