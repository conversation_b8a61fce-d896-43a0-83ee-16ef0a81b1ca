/**app.wxss**/
/* 全局基础样式 - DLAND风格 */
page {
  background-color: #F5F5F5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.container {
  padding: 32rpx;
  box-sizing: border-box;
  min-height: 100vh;
}

/* 统一配色方案 - 现代化配色 */
:root {
  --primary-color: #4A90E2;
  --secondary-color: #7ED321;
  --background-color: #F8F9FA;
  --card-color: #FFFFFF;
  --border-color: #E8F4FD;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --accent-color: #4A90E2;
  --success-color: #28A745;
  --warning-color: #FFC107;
  --danger-color: #DC3545;
  --gradient-primary: linear-gradient(135deg, #4A90E2 0%, #7ED321 100%);
  --gradient-secondary: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

/* 卡片样式 - 现代化设计 */
.card {
  background: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.card-shadow {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.card-padding {
  padding: 40rpx;
}

.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

/* 按钮样式 - 现代化设计 */
.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #7ED321 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:active::before {
  left: 100%;
}

.btn-primary:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.4);
}

.btn-secondary {
  background: #FFFFFF;
  color: #4A90E2;
  border: 2rpx solid #4A90E2;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  height: 96rpx;
  line-height: 92rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.1);
}

.btn-secondary:active {
  background: #F0F7FF;
  transform: scale(0.96);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
}

.btn-large {
  height: 112rpx;
  line-height: 112rpx;
  font-size: 36rpx;
  border-radius: 28rpx;
}

.btn-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  height: 180rpx;
  border-radius: 24rpx;
  background: #FFFFFF;
  border: 2rpx solid #E8F4FD;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.btn-icon:active {
  background: #F8FCFF;
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-icon-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.btn-icon-emoji {
  font-size: 52rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

/* 特殊按钮样式 */
.btn-gradient {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-gradient:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

/* 文字样式 - 更精细的层次 */
.title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.subtitle {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.body {
  font-size: 32rpx;
  color: var(--text-primary);
  line-height: 1.5;
}

.caption {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.footnote {
  font-size: 24rpx;
  color: var(--text-tertiary);
  line-height: 1.3;
}

.text-accent {
  color: var(--accent-color);
}

/* 头像样式 */
.avatar {
  border-radius: 50%;
  overflow: hidden;
  background: var(--border-color);
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-medium {
  width: 80rpx;
  height: 80rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 间距工具类 */
.mt-8 { margin-top: 8rpx; }
.mt-16 { margin-top: 16rpx; }
.mt-24 { margin-top: 24rpx; }
.mt-32 { margin-top: 32rpx; }
.mb-8 { margin-bottom: 8rpx; }
.mb-16 { margin-bottom: 16rpx; }
.mb-24 { margin-bottom: 24rpx; }
.mb-32 { margin-bottom: 32rpx; }
.mr-8 { margin-right: 8rpx; }
.mr-16 { margin-right: 16rpx; }
.mr-24 { margin-right: 24rpx; }
.ml-8 { margin-left: 8rpx; }
.ml-16 { margin-left: 16rpx; }
.ml-24 { margin-left: 24rpx; }

/* 入场券样式 - DLAND风格 */
.ticket {
  background: var(--card-color);
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.ticket::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -15rpx;
  width: 30rpx;
  height: 30rpx;
  background: var(--background-color);
  border-radius: 50%;
  transform: translateY(-50%);
}

.ticket::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -15rpx;
  width: 30rpx;
  height: 30rpx;
  background: var(--background-color);
  border-radius: 50%;
  transform: translateY(-50%);
}

.ticket-content {
  padding: 32rpx 48rpx;
}

/* 虚线分割线 */
.dashed-line {
  border-top: 2rpx dashed var(--border-color);
  margin: 24rpx 0;
}

/* 角色徽章 */
.role-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
}

.role-badge.main_captain {
  background: #2C2C2E;
  color: #FFFFFF;
}

.role-badge.vice_captain {
  background: var(--text-secondary);
  color: #FFFFFF;
}

.role-badge.member {
  background: var(--border-color);
  color: var(--text-secondary);
}

/* 动画效果 - 增强版 */
.scale-animation {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-animation:active {
  transform: scale(0.96);
}

.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.4);
  }
  70% {
    box-shadow: 0 0 0 20rpx rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

/* 悬浮效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

/* 加载动画 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E8F4FD;
  border-top: 4rpx solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成员选择按钮样式 */
.member-btn {
  background: #FFFFFF;
  color: #666666;
  border: 2rpx solid #E8F4FD;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  height: 72rpx;
  line-height: 68rpx;
  text-align: center;
  padding: 0 24rpx;
  margin: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.member-btn.active {
  background: var(--gradient-primary);
  color: #FFFFFF;
  border-color: #4A90E2;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.25);
}

.member-btn:active {
  transform: scale(0.96);
}

/* 导入方法选择样式 */
.import-method {
  background: #FFFFFF;
  border: 2rpx solid #E8F4FD;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.import-method.active {
  border-color: #4A90E2;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.15);
  background: #F8FCFF;
}

.import-method:active {
  transform: translateY(-2rpx);
}

/* 上传按钮样式 */
.upload-btn {
  width: 100%;
  height: 160rpx;
  background: #F8FCFF;
  border: 2rpx dashed #4A90E2;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:active {
  background: #E8F4FD;
  transform: scale(0.98);
}

/* 统计数字样式 */
.stat-number {
  font-size: 40rpx;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* 表单样式 - 现代化设计 */
.form-group {
  margin-bottom: 32rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 96rpx;
  background: #FFFFFF;
  border: 2rpx solid #E8F4FD;
  border-radius: 20rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-input:focus {
  border-color: #4A90E2;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.15);
  transform: translateY(-2rpx);
}

.form-input.placeholder {
  color: #999999;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: #FFFFFF;
  border: 2rpx solid #E8F4FD;
  border-radius: 20rpx;
  padding: 32rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-textarea:focus {
  border-color: #4A90E2;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.15);
  transform: translateY(-2rpx);
}

.form-textarea.placeholder {
  color: #999999;
}

/* 选择器样式 */
.form-picker {
  width: 100%;
  height: 96rpx;
  background: #FFFFFF;
  border: 2rpx solid #E8F4FD;
  border-radius: 20rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-picker:active {
  border-color: #4A90E2;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.15);
  transform: translateY(-2rpx);
}

.form-picker-placeholder {
  color: #999999;
}

.form-picker-arrow {
  width: 24rpx;
  height: 24rpx;
  border-right: 3rpx solid #999999;
  border-bottom: 3rpx solid #999999;
  transform: rotate(45deg);
  transition: transform 0.3s ease;
}

/* 空状态样式 */
.empty-state {
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  line-height: 1;
  opacity: 0.6;
  margin-bottom: 24rpx;
}

/* 浮动按钮 - 增强版 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2 0%, #7ED321 100%);
  color: #FFFFFF;
  border: none;
  box-shadow: 0 12rpx 32rpx rgba(74, 144, 226, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 52rpx;
  font-weight: 300;
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab:active {
  transform: scale(0.88);
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.6);
}

.fab.pulse {
  animation: fabPulse 2s infinite;
}

@keyframes fabPulse {
  0% {
    box-shadow: 0 12rpx 32rpx rgba(74, 144, 226, 0.4), 0 0 0 0 rgba(74, 144, 226, 0.4);
  }
  70% {
    box-shadow: 0 12rpx 32rpx rgba(74, 144, 226, 0.4), 0 0 0 20rpx rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 12rpx 32rpx rgba(74, 144, 226, 0.4), 0 0 0 0 rgba(74, 144, 226, 0);
  }
}