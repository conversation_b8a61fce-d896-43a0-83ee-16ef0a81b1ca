/**
 * 模拟数据文件
 * 用于前端开发阶段的数据模拟
 */

// 模拟用户数据
export const mockUsers = [
  {
    id: 'user001',
    openid: 'o6_bmjrPTlm6_2sgVt7hMZOPfL2M',
    nickname: '张三',
    avatar: '/images/avatar.png',
    phone: '13800138000',
    realName: '张三',
    role: 'main_captain'
  },
  {
    id: 'user002',
    openid: 'o6_bmjrPTlm6_2sgVt7hMZOPfL2N',
    nickname: '李四',
    avatar: '/images/avatar.png',
    phone: '13800138001',
    realName: '李四',
    role: 'member'
  },
  {
    id: 'user003',
    openid: 'o6_bmjrPTlm6_2sgVt7hMZOPfL2O',
    nickname: '王五',
    avatar: '/images/avatar.png',
    phone: '13800138002',
    realName: '王五',
    role: 'vice_captain'
  }
]

// 模拟队伍数据
export const mockTeams = [
  {
    id: 'team001',
    name: '梅里雪山徒步团',
    description: '探索藏区秘境，感受雪山魅力',
    mainCaptain: 'user001',
    viceCaptains: ['user003'],
    inviteCode: '123456',
    maxMembers: 30,
    memberCount: 15,
    status: 'active',
    createTime: '2024-01-15 09:00:00',
    members: [
      {
        ...mockUsers[0],
        joinTime: '2024-01-15 09:00:00',
        status: 'active'
      },
      {
        ...mockUsers[1],
        joinTime: '2024-01-15 10:30:00',
        status: 'active'
      },
      {
        ...mockUsers[2],
        joinTime: '2024-01-15 11:00:00',
        status: 'active'
      }
    ]
  },
  {
    id: 'team002',
    name: '泰山日出登山队',
    description: '凌晨出发，观赏壮美日出',
    mainCaptain: 'user001',
    viceCaptains: [],
    inviteCode: '789012',
    maxMembers: 20,
    memberCount: 8,
    status: 'active',
    createTime: '2024-01-20 14:00:00',
    members: [
      {
        ...mockUsers[0],
        joinTime: '2024-01-20 14:00:00',
        status: 'active'
      }
    ]
  }
]

// 模拟签到点数据
export const mockCheckinPoints = [
  {
    id: 'point001',
    teamId: 'team001',
    name: '集合出发点',
    location: {
      latitude: 39.908692,
      longitude: 116.397477,
      address: '北京市东城区天安门广场'
    },
    sequence: 1,
    radius: 500,
    createTime: '2024-01-15 09:00:00',
    checkedInCount: 12,
    totalCount: 15
  },
  {
    id: 'point002',
    teamId: 'team001',
    name: '上车集合点',
    location: {
      latitude: 39.915119,
      longitude: 116.403963,
      address: '北京市东城区王府井大街'
    },
    sequence: 2,
    radius: 300,
    createTime: '2024-01-15 09:00:00',
    checkedInCount: 8,
    totalCount: 15
  },
  {
    id: 'point003',
    teamId: 'team001',
    name: '服务区休息',
    location: {
      latitude: 39.928658,
      longitude: 116.388109,
      address: '北京市西城区什刹海'
    },
    sequence: 3,
    radius: 200,
    createTime: '2024-01-15 09:00:00',
    checkedInCount: 0,
    totalCount: 15
  }
]

// 模拟签到记录数据
export const mockCheckinRecords = [
  {
    id: 'record001',
    teamId: 'team001',
    pointId: 'point001',
    userId: 'user001',
    checkinTime: '2024-01-15 09:15:00',
    location: {
      latitude: 39.908692,
      longitude: 116.397477
    },
    type: 'normal',
    status: 'success'
  },
  {
    id: 'record002',
    teamId: 'team001',
    pointId: 'point001',
    userId: 'user002',
    checkinTime: '2024-01-15 09:18:00',
    location: {
      latitude: 39.908692,
      longitude: 116.397477
    },
    type: 'shout',
    status: 'success'
  }
]

// 当前用户信息（模拟登录状态）
export const currentUser = {
  ...mockUsers[0],
  isLoggedIn: true
}

// 模拟用户活动关系
export const mockUserActivities = [
  {
    userId: 'user001',
    activityId: 'activity001',
    role: 'main_captain',
    status: 'active',
    joinTime: '2024-01-15 09:00:00'
  },
  {
    userId: 'user002',
    activityId: 'activity001',
    role: 'member',
    status: 'active',
    joinTime: '2024-01-15 10:30:00'
  }
]

// 模拟活动数据
export const mockActivities = [
  {
    id: 'activity001',
    teamId: 'team001',
    name: '梅里雪山徒步活动',
    description: '2024年第一次团队徒步活动',
    captainId: 'user001',
    captainName: '张三',
    schedule: {
      meetingDate: '2024-02-01',
      meetingTime: '09:00',
      returnTime: '18:00'
    },
    meetingLocation: {
      latitude: 39.908692,
      longitude: 116.397477,
      address: '北京市东城区天安门广场'
    },
    status: 'active',
    createTime: '2024-01-15 09:00:00'
  }
]

// 模拟数据接口
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 获取用户信息
export const getUserInfo = async () => {
  await delay(500);
  return currentUser;
};

// 获取当前活动信息
export const getCurrentActivity = async () => {
  // TODO: 根据用户当前活动ID获取活动信息
  const app = getApp();
  const currentActivityId = app.getCurrentActivityId();
  
  if (currentActivityId) {
    const activity = mockActivities.find(a => a.id === currentActivityId);
    return activity || null;
  }
  
  // 如果没有当前活动ID，尝试从本地存储获取
  const currentActivity = wx.getStorageSync('currentActivity');
  if (currentActivity) {
    return currentActivity;
  }
  
  // 返回默认活动
  return mockActivities[0] || null;
};

// 获取团队统计信息
export const getTeamStats = async () => {
  await delay(500);
  return {
    total: 15,
    checkedIn: 8,
    arrived: 12
  };
};

// 获取队长喊话信息
export const getShoutInfo = async () => {
  await delay(500);
  return {
    id: 'shout001',
    captainId: 'user001',
    captainName: '张三',
    time: '09:30',
    isActive: true
  };
};

// 响应喊话
export const respondToShout = async () => {
  await delay(500);
  return { success: true };
};

// 签到
export const checkin = async () => {
  await delay(500);
  return { success: true };
};

// 创建活动
export const createActivity = async (activityData) => {
  await delay(1000);
  
  const newActivity = {
    id: 'activity' + Date.now(),
    ...activityData,
    inviteCode: Math.random().toString().slice(-6),
    createTime: new Date().toLocaleString(),
    status: 'active',
    captainId: currentUser.id,
    captainName: currentUser.nickname
  };
  
  // 模拟保存到本地存储
  const activities = wx.getStorageSync('activities') || {};
  activities[newActivity.code || newActivity.inviteCode] = newActivity;
  wx.setStorageSync('activities', activities);
  
  // 设置当前活动
  wx.setStorageSync('currentActivity', newActivity);
  wx.setStorageSync('userRole', 'captain');
  
  // 添加到活动列表
  mockActivities.push(newActivity);
  
  return { success: true, activityId: newActivity.id, data: newActivity };
};

// 通过口令加入活动
export const joinActivityByCode = async (code) => {
  await delay(500);
  
  // 先从本地存储查找
  const activities = wx.getStorageSync('activities') || {};
  let activity = activities[code];
  
  // 如果本地没有，从mock数据查找
  if (!activity) {
    activity = mockActivities.find(a => a.inviteCode === code);
  }
  
  if (activity) {
    // 设置用户角色和当前活动
    wx.setStorageSync('userRole', 'member');
    wx.setStorageSync('currentActivity', activity);
    wx.setStorageSync('activityCode', code);
    
    return { success: true, data: activity, message: '验证成功' };
  } else {
    return { success: false, message: '口令无效或已过期' };
  }
};

// 获取活动列表
export const getActivityList = async () => {
  await delay(500);
  
  const activities = wx.getStorageSync('activities') || {};
  return Object.values(activities);
};

// 更新活动信息
export const updateActivity = async (activityId, updateData) => {
  await delay(500);
  
  const activities = wx.getStorageSync('activities') || {};
  
  // 找到对应的活动并更新
  for (const code in activities) {
    if (activities[code].id === activityId) {
      activities[code] = { ...activities[code], ...updateData };
      wx.setStorageSync('activities', activities);
      
      // 如果是当前活动，也更新当前活动缓存
      const currentActivity = wx.getStorageSync('currentActivity');
      if (currentActivity && currentActivity.id === activityId) {
        wx.setStorageSync('currentActivity', activities[code]);
      }
      
      return { success: true };
    }
  }
  
  throw new Error('活动不存在');
};

// 发起喊话
export const createShout = async (content) => {
  await delay(500);
  
  const shoutData = {
    id: Date.now().toString(),
    content: content || '队长正在清点人数',
    captainId: 'user001',
    captainName: '张队长',
    time: new Date().toLocaleTimeString().slice(0, 5),
    isActive: true,
    responses: [],
    createTime: new Date().toISOString()
  };
  
  // 保存喊话记录
  const shouts = wx.getStorageSync('shouts') || [];
  shouts.unshift(shoutData);
  wx.setStorageSync('shouts', shouts);
  
  return { success: true, shout: shoutData };
};

// 获取成员管理数据
export const getMemberManageData = async () => {
  await delay(500);
  
  const currentActivity = wx.getStorageSync('currentActivity');
  if (!currentActivity) {
    return { members: [], stats: { total: 0, checkedIn: 0, arrived: 0 } };
  }
  
  // 模拟成员状态
  const members = currentActivity.members.map((member, index) => {
    const statuses = ['checked_in', 'arrived', 'no_response'];
    const status = statuses[index % 3];
    
    return {
      ...member,
      status,
      checkinTime: status === 'checked_in' ? '09:' + (10 + index).toString().padStart(2, '0') : null
    };
  });
  
  const stats = {
    total: members.length,
    checkedIn: members.filter(m => m.status === 'checked_in').length,
    arrived: members.filter(m => m.status === 'arrived').length
  };
  
  return { members, stats };
};

// 导出签到数据
export const exportCheckinData = async () => {
  await delay(1000);
  
  const currentActivity = wx.getStorageSync('currentActivity');
  if (!currentActivity) {
    throw new Error('没有当前活动');
  }
  
  // 模拟生成导出数据
  const exportData = {
    activityName: currentActivity.name,
    exportTime: new Date().toISOString(),
    members: currentActivity.members,
    summary: {
      total: currentActivity.members.length,
      checkedIn: Math.floor(currentActivity.members.length * 0.8),
      arrived: Math.floor(currentActivity.members.length * 0.6)
    }
  };
  
  return { success: true, data: exportData };
};
