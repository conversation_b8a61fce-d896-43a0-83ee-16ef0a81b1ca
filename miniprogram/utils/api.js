/**
 * API服务文件
 * 提供模拟接口和实际云函数调用的统一接口
 */

import { mockUsers, mockTeams, mockCheckinPoints, mockCheckinRecords, currentUser } from './mockData.js'

// 配置选项
const API_CONFIG = {
  useMockData: true, // 开发阶段使用模拟数据
  baseUrl: '',
  timeout: 5000
}

/**
 * 模拟异步请求
 * @param {*} data 返回的数据
 * @param {number} delay 延迟时间（毫秒）
 */
const mockRequest = (data, delay = 500) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: data,
        message: '操作成功'
      })
    }, delay)
  })
}

/**
 * 用户相关API
 */
export const userAPI = {
  // 获取当前用户信息
  getCurrentUser() {
    if (API_CONFIG.useMockData) {
      return mockRequest(currentUser)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'user',
      data: { action: 'getCurrentUser' }
    })
  },

  // 用户登录
  login(userInfo) {
    if (API_CONFIG.useMockData) {
      return mockRequest({ ...currentUser, ...userInfo })
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'user',
      data: { action: 'login', userInfo }
    })
  },

  // 更新用户信息
  updateProfile(profileData) {
    if (API_CONFIG.useMockData) {
      return mockRequest({ ...currentUser, ...profileData })
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'user',
      data: { action: 'updateProfile', profileData }
    })
  }
}

/**
 * 队伍相关API
 */
export const teamAPI = {
  // 获取我的队伍列表
  getMyTeams() {
    if (API_CONFIG.useMockData) {
      return mockRequest(mockTeams)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'team',
      data: { action: 'getMyTeams' }
    })
  },

  // 创建队伍
  createTeam(teamData) {
    if (API_CONFIG.useMockData) {
      const newTeam = {
        id: 'team' + Date.now(),
        ...teamData,
        mainCaptain: currentUser.id,
        viceCaptains: [],
        inviteCode: Math.random().toString().slice(-6),
        memberCount: 1,
        status: 'active',
        createTime: new Date().toLocaleString(),
        members: [currentUser]
      }
      return mockRequest(newTeam, 1000)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'team',
      data: { action: 'createTeam', teamData }
    })
  },

  // 加入队伍
  joinTeam(inviteCode) {
    if (API_CONFIG.useMockData) {
      const team = mockTeams.find(t => t.inviteCode === inviteCode)
      if (team) {
        return mockRequest(team)
      } else {
        return Promise.resolve({
          success: false,
          message: '邀请码无效或队伍不存在'
        })
      }
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'team',
      data: { action: 'joinTeam', inviteCode }
    })
  },

  // 获取队伍详情
  getTeamDetail(teamId) {
    if (API_CONFIG.useMockData) {
      const team = mockTeams.find(t => t.id === teamId)
      if (team) {
        return mockRequest({
          ...team,
          checkinPoints: mockCheckinPoints.filter(p => p.teamId === teamId)
        })
      }
      return Promise.resolve({
        success: false,
        message: '队伍不存在'
      })
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'team',
      data: { action: 'getTeamDetail', teamId }
    })
  },

  // 批量导入成员（Excel）
  importMembers(teamId, members) {
    if (API_CONFIG.useMockData) {
      // 模拟Excel导入成功
      const importedMembers = members.map((member, index) => ({
        id: 'imported_' + Date.now() + '_' + index,
        nickname: member.name,
        phone: member.phone,
        realName: member.name,
        avatar: '/images/avatar.png',
        role: 'member',
        joinTime: new Date().toLocaleString(),
        status: 'active'
      }))
      return mockRequest({
        importedCount: importedMembers.length,
        members: importedMembers
      }, 2000)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'team',
      data: { action: 'importMembers', teamId, members }
    })
  }
}

/**
 * 签到相关API
 */
export const checkinAPI = {
  // 创建签到点
  createCheckinPoint(pointData) {
    if (API_CONFIG.useMockData) {
      const newPoint = {
        id: 'point' + Date.now(),
        ...pointData,
        createTime: new Date().toLocaleString(),
        checkedInCount: 0,
        totalCount: mockTeams.find(t => t.id === pointData.teamId)?.memberCount || 0
      }
      return mockRequest(newPoint)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'checkin',
      data: { action: 'createPoint', pointData }
    })
  },

  // 获取队伍签到点列表
  getCheckinPoints(teamId) {
    if (API_CONFIG.useMockData) {
      const points = mockCheckinPoints.filter(p => p.teamId === teamId)
      return mockRequest(points)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'checkin',
      data: { action: 'getPoints', teamId }
    })
  },

  // 执行签到
  performCheckin(checkinData) {
    if (API_CONFIG.useMockData) {
      // 模拟签到成功
      const record = {
        id: 'record' + Date.now(),
        ...checkinData,
        userId: currentUser.id,
        checkinTime: new Date().toLocaleString(),
        type: 'normal',
        status: 'success'
      }
      return mockRequest(record, 800)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'checkin',
      data: { action: 'checkin', checkinData }
    })
  },

  // 喊道报到
  performShout(shoutData) {
    if (API_CONFIG.useMockData) {
      // 模拟喊道成功
      const record = {
        id: 'shout' + Date.now(),
        ...shoutData,
        userId: currentUser.id,
        checkinTime: new Date().toLocaleString(),
        type: 'shout',
        status: 'success'
      }
      return mockRequest(record, 300)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'checkin',
      data: { action: 'shout', shoutData }
    })
  },

  // 获取签到记录
  getCheckinRecords(teamId, pointId) {
    if (API_CONFIG.useMockData) {
      let records = mockCheckinRecords.filter(r => r.teamId === teamId)
      if (pointId) {
        records = records.filter(r => r.pointId === pointId)
      }
      return mockRequest(records)
    }
    // TODO: 实际云函数调用
    return wx.cloud.callFunction({
      name: 'checkin',
      data: { action: 'getRecords', teamId, pointId }
    })
  }
}

/**
 * 工具函数
 */
export const utilsAPI = {
  // 计算两点距离（米）
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3 // 地球半径（米）
    const φ1 = lat1 * Math.PI / 180
    const φ2 = lat2 * Math.PI / 180
    const Δφ = (lat2 - lat1) * Math.PI / 180
    const Δλ = (lon2 - lon1) * Math.PI / 180

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) *
      Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  },

  // 格式化时间
  formatTime(time) {
    if (typeof time === 'string') {
      return time
    }
    const date = new Date(time)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 生成邀请码
  generateInviteCode() {
    return Math.random().toString(36).substr(2, 6).toUpperCase()
  }
}

// 导出API配置，方便外部切换模式
export { API_CONFIG }
