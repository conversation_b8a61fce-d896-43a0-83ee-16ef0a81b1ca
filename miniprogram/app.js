// app.js
App({
  onLaunch: function () {
    this.globalData = {
      // env 参数说明：
      //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
      //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
      //   如不填则使用默认环境（第一个创建的环境）
      env: "cloud1-5gozwbxlb81b2ff3",
      userInfo: null,
      currentActivityId: null, // 当前查看的活动ID
      userActivities: [], // 用户参与的所有活动 [{activityId, role, status}]
      activityList: [] // 用户可见的活动列表
    };
    
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
    }
    
    // 初始化用户信息
    this.initUserInfo();
  },

  // 初始化用户信息
  async initUserInfo() {
    try {
      // 从缓存获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      const currentActivityId = wx.getStorageSync('currentActivityId');
      const userActivities = wx.getStorageSync('userActivities') || [];
      
      if (userInfo) {
        this.globalData.userInfo = userInfo;
        this.globalData.currentActivityId = currentActivityId;
        this.globalData.userActivities = userActivities;
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error);
    }
  },

  // 设置当前活动
  setCurrentActivity(activityId) {
    this.globalData.currentActivityId = activityId;
    
    // 保存到本地存储
    try {
      wx.setStorageSync('currentActivityId', activityId);
    } catch (error) {
      console.error('保存当前活动失败:', error);
    }
  },

  // 获取当前活动ID
  getCurrentActivityId() {
    return this.globalData.currentActivityId;
  },

  // 获取用户在指定活动中的角色
  getUserRoleInActivity(activityId) {
    const activity = this.globalData.userActivities.find(a => a.activityId === activityId);
    return activity ? activity.role : null;
  },

  // 添加用户活动
  addUserActivity(activityId, role, status = 'active') {
    const existingIndex = this.globalData.userActivities.findIndex(a => a.activityId === activityId);
    
    if (existingIndex >= 0) {
      // 更新现有活动
      this.globalData.userActivities[existingIndex] = { activityId, role, status };
    } else {
      // 添加新活动
      this.globalData.userActivities.push({ activityId, role, status });
    }
    
    // 保存到本地存储
    try {
      wx.setStorageSync('userActivities', this.globalData.userActivities);
    } catch (error) {
      console.error('保存用户活动失败:', error);
    }
  },

  // 根据角色导航到对应页面
  navigateByRole(activityId = null) {
    const currentActivityId = activityId || this.globalData.currentActivityId;
    
    if (!currentActivityId) {
      wx.redirectTo({
        url: '/pages/role-select/role-select'
      });
      return;
    }
    
    const role = this.getUserRoleInActivity(currentActivityId);
    
    if (role === 'captain' || role === 'main_captain') {
      wx.redirectTo({
        url: '/pages/captain-dashboard/captain-dashboard'
      });
    } else if (role === 'member' || role === 'vice_captain') {
      wx.redirectTo({
        url: '/pages/member-dashboard/member-dashboard'
      });
    } else {
      wx.redirectTo({
        url: '/pages/role-select/role-select'
      });
    }
  }
});