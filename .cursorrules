# 微信小程序开发规则 (WeChat Mini Program Development Rules)

## 项目结构规范
- 使用标准的微信小程序目录结构
- pages/ 目录按功能模块组织，每个页面包含 .wxml, .wxss, .js, .json 四个文件
- components/ 目录存放可复用组件
- utils/ 目录存放工具函数和API封装
- images/ 目录存放静态资源，按模块分类

## 代码风格规范

### JavaScript 规范
- 使用 ES6+ 语法，优先使用 const 和 let
- 函数命名使用驼峰命名法，组件方法以动词开头
- 异步操作优先使用 Promise 和 async/await
- 避免使用 var，使用严格模式
- 每个文件末尾保留一个空行

### WXML 规范
- 使用语义化的标签和类名
- 属性按照：结构属性、样式属性、事件属性的顺序排列
- 使用双引号包裹属性值
- 合理使用 wx:if 和 hidden，优先使用 wx:if
- 列表渲染必须添加 wx:key

### WXSS 规范
- 使用 rpx 作为主要单位，适配不同屏幕
- 遵循 BEM 命名规范或语义化类名
- 避免使用 !important
- 合理使用 flex 布局
- 颜色值使用十六进制或 CSS 变量

## 微信小程序特定规范

### 页面生命周期
- 在 onLoad 中进行数据初始化
- 在 onShow 中处理页面显示逻辑
- 在 onHide 中清理定时器和监听器
- 在 onUnload 中进行资源清理

### 数据绑定
- 使用 setData 更新数据，避免直接修改 data
- setData 的数据量要控制，避免性能问题
- 使用对象路径更新嵌套数据：'obj.key': value

### 事件处理
- 事件处理函数以 handle 或 on 开头
- 使用 dataset 传递数据：data-xxx
- 合理使用事件冒泡和捕获

### API 调用
- 优先使用 Promise 化的 API
- 统一封装网络请求，处理错误和加载状态
- 合理使用缓存机制
- 注意 API 的兼容性和权限

## 性能优化
- 图片使用合适的格式和尺寸
- 合理使用分包加载
- 避免频繁的 setData 调用
- 使用 IntersectionObserver 处理长列表
- 及时清理定时器和事件监听

## 组件开发
- 组件属性使用 properties 定义
- 组件事件使用 triggerEvent 触发
- 合理使用 slot 插槽
- 组件样式使用 addGlobalClass 或样式隔离

## 云开发规范
- 云函数按功能模块组织
- 数据库操作使用事务处理
- 合理设计数据库权限
- 云存储文件命名规范化

## 安全规范
- 敏感信息不要硬编码在前端
- 用户输入要进行验证和过滤
- 合理使用云函数处理敏感操作
- 注意用户隐私保护

## 调试和测试
- 使用 console.log 进行调试，发布前清理
- 在不同设备和网络环境下测试
- 使用微信开发者工具的性能面板
- 编写单元测试覆盖核心逻辑

## 发布规范
- 代码压缩和混淆
- 检查并清理无用代码和资源
- 版本号遵循语义化版本规范
- 提交前进行代码审查

## 常用代码模式

### 页面模板
```javascript
Page({
  data: {
    // 页面数据
  },
  
  onLoad(options) {
    // 页面加载
  },
  
  onShow() {
    // 页面显示
  },
  
  // 事件处理函数
  handleTap(e) {
    const { dataset } = e.currentTarget;
    // 处理逻辑
  }
});
```

### 组件模板
```javascript
Component({
  properties: {
    // 组件属性
  },
  
  data: {
    // 组件数据
  },
  
  methods: {
    // 组件方法
  }
});
```

### API 封装
```javascript
const request = (url, data = {}, method = 'GET') => {
  return new Promise((resolve, reject) => {
    wx.request({
      url,
      data,
      method,
      success: resolve,
      fail: reject
    });
  });
};
```

## 注释规范
- 文件头部添加功能说明注释
- 复杂逻辑添加行内注释
- 函数添加 JSDoc 格式注释
- 组件属性和方法添加说明注释

遵循这些规则可以确保微信小程序代码的质量、可维护性和团队协作效率。